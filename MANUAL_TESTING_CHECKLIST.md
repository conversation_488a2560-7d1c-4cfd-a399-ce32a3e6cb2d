# Arena Doviz - Manual Testing Checklist

**Purpose:** Complete the end-to-end transaction workflow verification  
**Status:** Ready for manual testing - all infrastructure issues resolved  
**Prerequisites:** Django server running, test data available  

## Pre-Testing Setup

### 1. Start the System
```bash
cd src
set ARENA_ENCRYPTION_KEY=x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=
python manage.py runserver 0.0.0.0:8000 --settings=config.settings.dev
```

### 2. Access the System
- Open browser: http://localhost:8000
- Login credentials: Use existing admin user or create new user

### 3. Open Browser Console
- Press F12 to open Developer Tools
- Go to Console tab
- Monitor for any JavaScript errors during testing

---

## Phase 1: Authentication & Navigation Testing

### ✅ Login Process
- [ ] Navigate to login page
- [ ] Enter valid credentials
- [ ] Verify successful login and redirect to dashboard
- [ ] Check console for errors (should be zero)

### ✅ Dashboard Verification
- [ ] Dashboard loads without errors
- [ ] All navigation menu items are visible
- [ ] Statistics cards display properly
- [ ] No JavaScript errors in console

### ✅ Navigation Testing
- [ ] Click "Customers" - page loads correctly
- [ ] Click "Transactions" - page loads correctly  
- [ ] Click "Reports" - page loads correctly
- [ ] All pages load without console errors

---

## Phase 2: Dropdown Population Testing

### ✅ Customer Dropdown Testing
Navigate to any transaction form and verify:
- [ ] Customer dropdown populates with available customers
- [ ] Can select a customer from dropdown
- [ ] Customer selection triggers any balance display updates

### ✅ Currency Dropdown Testing
- [ ] From Currency dropdown shows: USD, AED, IRR (minimum)
- [ ] To Currency dropdown shows: USD, AED, IRR (minimum)
- [ ] Currency selection works properly

### ✅ Location Dropdown Testing
- [ ] Location dropdown shows: Istanbul, Dubai, Tehran, etc.
- [ ] Can select location successfully

### ✅ Transaction Type Testing
- [ ] All 6 transaction types are available:
  - [ ] Currency Exchange
  - [ ] Money Transfer  
  - [ ] Cash Deposit
  - [ ] Cash Withdrawal
  - [ ] Remittance
  - [ ] Balance Adjustment

---

## Phase 3: Transaction Workflow Testing

### Test Customer Setup
Create or use existing test customer:
- Name: Test Customer
- Phone: +************
- Email: <EMAIL>

### ✅ Transaction 1: Cash Deposit ($1,000 USD)
Navigate to Cash Deposit form:
- [ ] Form loads without errors
- [ ] Select test customer from dropdown
- [ ] Select USD currency
- [ ] Enter amount: 1000
- [ ] Select delivery method: Cash
- [ ] **Test Courier Selection:**
  - [ ] Set delivery method to "Courier"
  - [ ] Courier dropdown appears
  - [ ] Can select system courier (if available)
  - [ ] Can select customer-specific courier (if available)
  - [ ] No "UUID validation" errors occur
- [ ] Add description: "Test cash deposit"
- [ ] Submit transaction
- [ ] Verify success message
- [ ] Check customer balance increased to $1,000

### ✅ Transaction 2: Cash Withdrawal ($300 USD)
Navigate to Cash Withdrawal form:
- [ ] Select same test customer
- [ ] Select USD currency
- [ ] Enter amount: 300
- [ ] **Test Courier Assignment:**
  - [ ] Select courier for withdrawal
  - [ ] Courier selection works without errors
- [ ] Submit transaction
- [ ] Verify success message
- [ ] Check customer balance decreased to $700

### ✅ Transaction 3: Internal Transfer ($200 USD)
Create second test customer first, then:
- [ ] Navigate to Money Transfer form
- [ ] Select first test customer (sender)
- [ ] Enter amount: 200
- [ ] In recipient details, specify second customer
- [ ] Submit transfer
- [ ] Verify sender balance: $500
- [ ] Verify receiver balance: +$200

### ✅ Transaction 4: External Bank Transfer ($150 USD)
- [ ] Navigate to Bank Transfer form
- [ ] Select test customer
- [ ] Enter amount: 150
- [ ] Fill bank details:
  - [ ] Bank name: "Test Bank"
  - [ ] Account number: "*********"
  - [ ] SWIFT code: "TESTUS33"
- [ ] Submit transfer
- [ ] Verify customer balance: $350
- [ ] Check transaction appears in history

### ✅ Transaction 5: International Transfer with Conversion ($100 USD → AED)
- [ ] Navigate to International Transfer form
- [ ] Select test customer
- [ ] From currency: USD
- [ ] To currency: AED
- [ ] Amount: 100 USD
- [ ] **Verify Exchange Rate Calculation:**
  - [ ] Exchange rate appears automatically
  - [ ] Converted amount calculates correctly
  - [ ] Commission amount displays
- [ ] Fill recipient details (international)
- [ ] Submit transfer
- [ ] Verify calculations are accurate
- [ ] Check final customer balance

---

## Phase 4: Balance Display Verification

### ✅ Real-time Balance Updates
After each transaction:
- [ ] Customer balance displays correctly
- [ ] Balance format shows proper currency symbols
- [ ] Multi-currency balances (if applicable) display properly
- [ ] Balance updates immediately after transaction

### ✅ Balance History
- [ ] Transaction history shows all 5 test transactions
- [ ] Running balance is accurate
- [ ] Transaction dates and descriptions are correct

---

## Phase 5: Document Upload Testing

### ✅ File Upload Functionality
For any transaction form:
- [ ] Document upload field is present
- [ ] Can select file from computer
- [ ] File uploads successfully
- [ ] No errors during upload process
- [ ] Uploaded document associates with transaction

---

## Phase 6: Report Generation Testing

### ✅ Customer Statement Generation
- [ ] Navigate to Reports section
- [ ] Select customer-specific report
- [ ] Choose test customer
- [ ] Generate statement
- [ ] **Verify Statement Content:**
  - [ ] All 5 transactions appear
  - [ ] Transaction amounts are correct
  - [ ] Running balance is accurate
  - [ ] Dates and descriptions match
- [ ] Export/PDF functionality works (if available)

---

## Phase 7: Error Handling Testing

### ✅ Form Validation
- [ ] Try submitting empty transaction form
- [ ] Verify validation messages appear
- [ ] Try invalid amounts (negative, zero)
- [ ] Verify proper error handling

### ✅ Network Error Handling
- [ ] Temporarily disconnect internet
- [ ] Try submitting transaction
- [ ] Verify graceful error handling
- [ ] Reconnect and verify recovery

---

## Phase 8: Browser Console Verification

### ✅ Final Console Check
After completing all tests:
- [ ] Open browser console (F12)
- [ ] Verify ZERO JavaScript errors
- [ ] Check Network tab for failed requests
- [ ] Verify all static files loaded (200 status)

### ✅ Error Monitor Check
If error monitoring is active:
- [ ] Run: `getErrorReport()` in console
- [ ] Verify zero errors reported
- [ ] Check error monitor is functioning

---

## Phase 9: Responsive Design Testing

### ✅ Mobile Testing
- [ ] Resize browser to mobile size (375px width)
- [ ] Verify forms are usable on mobile
- [ ] Test transaction form on mobile
- [ ] Verify navigation works on mobile

### ✅ Tablet Testing
- [ ] Resize browser to tablet size (768px width)
- [ ] Verify layout adapts properly
- [ ] Test key functionality on tablet size

---

## Phase 10: Security Testing

### ✅ Authentication Security
- [ ] Try accessing protected pages without login
- [ ] Verify redirect to login page
- [ ] Test logout functionality
- [ ] Verify session management

### ✅ CSRF Protection
- [ ] Verify forms have CSRF tokens
- [ ] Check form submissions work properly
- [ ] No CSRF-related errors

---

## Final Verification Checklist

### ✅ System Health Summary
- [ ] All 5 transactions completed successfully
- [ ] Customer balances are accurate
- [ ] No JavaScript console errors
- [ ] All static files loading (200 status)
- [ ] Courier selection working properly
- [ ] Document upload functional
- [ ] Reports generating correctly
- [ ] Error monitoring active
- [ ] Responsive design working

### ✅ Production Readiness
- [ ] Zero critical errors found
- [ ] All transaction types functional
- [ ] User experience is smooth
- [ ] Performance is acceptable
- [ ] Security measures working

---

## Issue Reporting Template

If any issues are found during testing:

**Issue:** [Brief description]  
**Steps to Reproduce:**  
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Result:** [What should happen]  
**Actual Result:** [What actually happened]  
**Browser Console Errors:** [Any JavaScript errors]  
**Screenshot:** [If applicable]  
**Priority:** [High/Medium/Low]

---

## Success Criteria

✅ **TESTING COMPLETE** when:
- All checklist items are marked complete
- Zero critical errors found
- All 5 transaction workflows successful
- Customer balances accurate
- Reports generate correctly
- System performs well across all test scenarios

**Upon successful completion:** System is approved for production deployment.

---

*This checklist ensures comprehensive verification of the Arena Doviz transaction system. Complete all items systematically and report any issues found.*

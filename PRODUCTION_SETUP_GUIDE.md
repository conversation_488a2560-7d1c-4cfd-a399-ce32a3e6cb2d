# Arena Doviz Production Setup Guide

## 🎯 **COMPLETE PRODUCTION SETUP**

This guide will help you set up and run Arena Doviz in production mode.

---

## **Prerequisites**

✅ **System Requirements:**
- Windows 10/11 or Windows Server
- Python 3.8+ (you have Python 3.13.6 ✓)
- Git (for version control)
- At least 4GB RAM
- 10GB free disk space

✅ **Already Completed:**
- ✅ Virtual environment created and activated
- ✅ Dependencies installed
- ✅ Database migrations run
- ✅ Static files collected
- ✅ Frontend-backend integration fixes applied

---

## **Step 1: Set Environment Variables**

### **Option A: Set for Current Session (Temporary)**
```cmd
# Command Prompt
set ARENA_ENCRYPTION_KEY=x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=

# PowerShell
$env:ARENA_ENCRYPTION_KEY="x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo="
```

### **Option B: Set Permanently (Recommended)**
1. **Windows System Environment Variables:**
   - Press `Win + R`, type `sysdm.cpl`, press Enter
   - Click "Environment Variables"
   - Under "System Variables", click "New"
   - Variable name: `ARENA_ENCRYPTION_KEY`
   - Variable value: `x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=`
   - Click OK and restart your terminal

2. **Or use PowerShell (Admin required):**
```powershell
[Environment]::SetEnvironmentVariable("ARENA_ENCRYPTION_KEY", "x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=", "Machine")
```

---

## **Step 2: Run the Production Server**

### **Method 1: Using the Batch File (Easiest)**
```cmd
# Navigate to project directory
cd C:\Users\<USER>\Documents\exchange-accounting

# Run the batch file
run_server.bat
```

### **Method 2: Using PowerShell Script**
```powershell
# Navigate to project directory
cd C:\Users\<USER>\Documents\exchange-accounting

# Run the PowerShell script
.\run_server.ps1
```

### **Method 3: Manual Command**
```cmd
# Navigate to src directory
cd C:\Users\<USER>\Documents\exchange-accounting\src

# Set encryption key (if not set permanently)
set ARENA_ENCRYPTION_KEY=x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=

# Run the server
python manage.py runserver 0.0.0.0:8000 --settings=config.settings.prod
```

---

## **Step 3: Access the Application**

Once the server is running, you can access Arena Doviz at:

- **Local Access:** http://localhost:8000
- **Network Access:** http://0.0.0.0:8000
- **External Access:** http://YOUR_SERVER_IP:8000

### **Default Login Credentials**
- **Username:** Your superuser account (created during setup)
- **Password:** Your superuser password

---

## **Step 4: Test the Fixes**

### **Test 1: Authentication/Logout**
1. Login to the system
2. Click the logout button
3. Confirm you're properly logged out (no auto re-login)

### **Test 2: Balance Validation**
1. Go to Transactions → Transfer → Internal Transfer
2. Select a customer with balance
3. Enter a transfer amount
4. Verify balance validation works correctly

### **Test 3: Transaction List**
1. Go to Transactions → List
2. Try to approve/reject a transaction
3. Verify the action works without JavaScript errors

### **Test 4: Transaction Forms**
1. Visit all transaction type pages:
   - Exchange
   - Internal Transfer
   - External Transfer
   - International Transfer
   - Deposit
   - Withdrawal
2. Verify no JavaScript console errors

---

## **Troubleshooting**

### **Issue: Server Won't Start**
```cmd
# Check if encryption key is set
echo %ARENA_ENCRYPTION_KEY%

# Check Django configuration
python manage.py check

# Run with verbose output
python manage.py runserver 0.0.0.0:8000 --verbosity=2
```

### **Issue: Database Errors**
```cmd
# Run migrations
python manage.py migrate

# Create cache table
python manage.py createcachetable
```

### **Issue: Static Files Not Loading**
```cmd
# Collect static files
python manage.py collectstatic --noinput
```

### **Issue: Permission Errors**
- Run terminal as Administrator
- Check file permissions in project directory

---

## **Production Configuration**

### **Current Production Settings:**
- **Debug:** Disabled ✓
- **Debug Toolbar:** Disabled ✓
- **Cache:** In-memory cache ✓
- **Static Files:** Collected ✓
- **Security:** HTTPS redirect disabled (for local testing)

### **For Real Production Deployment:**
1. **SSL/HTTPS Setup:**
   - Get SSL certificate
   - Configure reverse proxy (nginx/Apache)
   - Enable HTTPS redirect in settings

2. **Database:**
   - Consider PostgreSQL for production
   - Set up database backups

3. **Email Configuration:**
   - Configure SMTP settings
   - Set up email templates

4. **Monitoring:**
   - Set up logging
   - Configure error reporting
   - Monitor server resources

---

## **Security Notes**

⚠️ **Important Security Considerations:**

1. **Encryption Key:** Keep the encryption key secure and never commit it to version control
2. **Database:** Ensure database is properly secured
3. **Firewall:** Configure firewall rules for port 8000
4. **Updates:** Keep Python and dependencies updated
5. **Backups:** Regular database and file backups

---

## **Next Steps for Full Production**

1. **Domain Setup:**
   - Configure DNS
   - Set up SSL certificate
   - Configure web server (nginx/Apache)

2. **Email Configuration:**
   - SMTP server setup
   - Email templates customization

3. **Monitoring:**
   - Server monitoring
   - Application logging
   - Error tracking

4. **Backup Strategy:**
   - Database backups
   - File backups
   - Disaster recovery plan

---

## **Support**

If you encounter any issues:

1. Check the console output for error messages
2. Verify all environment variables are set
3. Ensure all dependencies are installed
4. Check file permissions
5. Review the troubleshooting section above

---

**🎉 Your Arena Doviz system is now ready for production use!**

The frontend-backend integration issues have been fixed and the system should work smoothly.

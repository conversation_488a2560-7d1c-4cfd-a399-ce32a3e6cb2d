#!/usr/bin/env python3
"""
Complete Arena Doviz Transaction Workflow Test

This script executes the comprehensive transaction workflow testing as specified:
1. Dropdown menu verification
2. Balance display verification  
3. Complete transaction sequence (5 transactions)
4. Statement generation
5. Issue resolution
6. Final system health check

Usage:
    python complete_workflow_test.py
"""

import os
import sys
import django
import time
import requests
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from apps.customers.models import Customer
from apps.currencies.models import Currency
from apps.locations.models import Location
from apps.transactions.models import Transaction, TransactionType

User = get_user_model()


class WorkflowTester:
    """Complete workflow testing for Arena Doviz."""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.test_results = {
            'phase1_dropdowns': {},
            'phase2_balances': {},
            'phase3_transactions': {},
            'phase4_reporting': {},
            'phase5_issues': [],
            'phase6_health': {},
            'summary': {}
        }
        self.test_customer = None
        self.test_customer2 = None
        self.initial_balance = Decimal('0.00')
        
    def setup_test_data(self):
        """Set up test data for workflow testing."""
        print("🔧 Setting up test data...")
        
        try:
            # Create test user
            self.test_user, created = User.objects.get_or_create(
                username='workflow_test_user',
                defaults={
                    'email': '<EMAIL>',
                    'first_name': 'Workflow',
                    'last_name': 'Tester',
                    'is_active': True,
                    'is_staff': True,
                }
            )
            if created:
                self.test_user.set_password('testpass123')
                self.test_user.save()
            
            # Create test customers
            self.test_customer, created = Customer.objects.get_or_create(
                phone_number='+971501111111',
                defaults={
                    'first_name': 'Ahmed',
                    'last_name': 'Al-Workflow',
                    'email': '<EMAIL>',
                    'customer_type': Customer.CustomerType.INDIVIDUAL,
                    'status': Customer.Status.ACTIVE,
                }
            )
            
            self.test_customer2, created = Customer.objects.get_or_create(
                phone_number='+971502222222',
                defaults={
                    'first_name': 'Fatima',
                    'last_name': 'Al-Transfer',
                    'email': '<EMAIL>',
                    'customer_type': Customer.CustomerType.INDIVIDUAL,
                    'status': Customer.Status.ACTIVE,
                }
            )
            
            print(f"✅ Test data setup complete")
            print(f"   Test Customer 1: {self.test_customer.get_display_name()}")
            print(f"   Test Customer 2: {self.test_customer2.get_display_name()}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error setting up test data: {e}")
            return False
    
    def test_server_connectivity(self):
        """Test if server is accessible."""
        print("🌐 Testing server connectivity...")
        
        try:
            response = requests.get(f"{self.base_url}/accounts/login/", timeout=10)
            if response.status_code in [200, 302]:
                print("✅ Server is accessible")
                return True
            else:
                print(f"❌ Server returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Server not accessible: {e}")
            return False
    
    def phase1_dropdown_verification(self):
        """Phase 1: Verify all dropdown menus populate correctly."""
        print("\n" + "="*60)
        print("PHASE 1: DROPDOWN MENU VERIFICATION")
        print("="*60)
        
        results = {}
        
        # Test customer dropdown data
        print("1.1 Testing Customer Dropdown Data...")
        try:
            customers = Customer.objects.filter(status=Customer.Status.ACTIVE)
            customer_count = customers.count()
            results['customers'] = {
                'count': customer_count,
                'status': 'PASS' if customer_count >= 2 else 'FAIL',
                'details': f"Found {customer_count} active customers"
            }
            print(f"   ✅ Customer dropdown: {customer_count} active customers")
        except Exception as e:
            results['customers'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Customer dropdown error: {e}")
        
        # Test currency dropdown data
        print("1.2 Testing Currency Dropdown Data...")
        try:
            currencies = Currency.objects.filter(is_active=True)
            currency_codes = list(currencies.values_list('code', flat=True))
            required_currencies = ['USD', 'AED', 'IRR']
            has_required = all(code in currency_codes for code in required_currencies)
            
            results['currencies'] = {
                'count': len(currency_codes),
                'codes': currency_codes,
                'has_required': has_required,
                'status': 'PASS' if has_required else 'FAIL',
                'details': f"Found currencies: {', '.join(currency_codes)}"
            }
            print(f"   ✅ Currency dropdown: {', '.join(currency_codes)}")
        except Exception as e:
            results['currencies'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Currency dropdown error: {e}")
        
        # Test location dropdown data
        print("1.3 Testing Location Dropdown Data...")
        try:
            locations = Location.objects.filter(is_active=True)
            location_names = list(locations.values_list('name', flat=True))
            required_locations = ['Istanbul', 'Dubai', 'Tehran']
            has_required = any(loc in location_names for loc in required_locations)
            
            results['locations'] = {
                'count': len(location_names),
                'names': location_names,
                'has_required': has_required,
                'status': 'PASS' if has_required else 'FAIL',
                'details': f"Found locations: {', '.join(location_names)}"
            }
            print(f"   ✅ Location dropdown: {', '.join(location_names)}")
        except Exception as e:
            results['locations'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Location dropdown error: {e}")
        
        # Test transaction type dropdown data
        print("1.4 Testing Transaction Type Data...")
        try:
            transaction_types = TransactionType.objects.filter(is_active=True)
            type_names = list(transaction_types.values_list('name', flat=True))
            
            results['transaction_types'] = {
                'count': len(type_names),
                'types': type_names,
                'status': 'PASS' if len(type_names) >= 5 else 'FAIL',
                'details': f"Found transaction types: {', '.join(type_names)}"
            }
            print(f"   ✅ Transaction types: {', '.join(type_names)}")
        except Exception as e:
            results['transaction_types'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Transaction types error: {e}")
        
        self.test_results['phase1_dropdowns'] = results
        
        # Summary
        passed = sum(1 for r in results.values() if r.get('status') == 'PASS')
        total = len(results)
        print(f"\n📊 Phase 1 Summary: {passed}/{total} dropdown tests passed")
        
        return passed == total
    
    def phase2_balance_verification(self):
        """Phase 2: Verify customer balance displays."""
        print("\n" + "="*60)
        print("PHASE 2: BALANCE DISPLAY VERIFICATION")
        print("="*60)
        
        results = {}
        
        print("2.1 Testing Customer Balance Retrieval...")
        try:
            # Get customer balances
            from apps.customers.services import CustomerBalanceService
            
            balance_service = CustomerBalanceService()
            customer1_balances = balance_service.get_customer_balances(self.test_customer)
            customer2_balances = balance_service.get_customer_balances(self.test_customer2)
            
            results['balance_retrieval'] = {
                'customer1_balances': customer1_balances,
                'customer2_balances': customer2_balances,
                'status': 'PASS',
                'details': 'Balance retrieval working'
            }
            
            print(f"   ✅ Customer 1 balances: {customer1_balances}")
            print(f"   ✅ Customer 2 balances: {customer2_balances}")
            
        except Exception as e:
            results['balance_retrieval'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Balance retrieval error: {e}")
        
        print("2.2 Testing Balance Display Format...")
        try:
            # Test balance formatting
            test_amounts = [
                (Decimal('1000.50'), 'USD'),
                (Decimal('3673.25'), 'AED'),
                (Decimal('42000000'), 'IRR')
            ]
            
            formatted_balances = []
            for amount, currency_code in test_amounts:
                try:
                    currency = Currency.objects.get(code=currency_code)
                    formatted = f"{currency.symbol}{amount:,.2f}"
                    formatted_balances.append(f"{currency_code}: {formatted}")
                except Currency.DoesNotExist:
                    formatted_balances.append(f"{currency_code}: {amount}")
            
            results['balance_formatting'] = {
                'formatted_balances': formatted_balances,
                'status': 'PASS',
                'details': 'Balance formatting working'
            }
            
            print(f"   ✅ Balance formatting test:")
            for formatted in formatted_balances:
                print(f"      {formatted}")
                
        except Exception as e:
            results['balance_formatting'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Balance formatting error: {e}")
        
        self.test_results['phase2_balances'] = results
        
        # Summary
        passed = sum(1 for r in results.values() if r.get('status') == 'PASS')
        total = len(results)
        print(f"\n📊 Phase 2 Summary: {passed}/{total} balance tests passed")
        
        return passed == total
    
    def phase3_transaction_workflow(self):
        """Phase 3: Execute complete transaction workflow."""
        print("\n" + "="*60)
        print("PHASE 3: COMPLETE TRANSACTION WORKFLOW")
        print("="*60)
        
        results = {}
        
        # Get required objects
        try:
            usd = Currency.objects.get(code='USD')
            location = Location.objects.filter(is_active=True).first()
            
            if not location:
                print("❌ No active location found")
                return False
                
        except Exception as e:
            print(f"❌ Error getting required objects: {e}")
            return False
        
        # Transaction 1: Cash Deposit
        print("3.1 Testing Cash Deposit Transaction...")
        try:
            deposit_data = {
                'transaction_type': 'DEPOSIT',
                'customer': self.test_customer,
                'location': location,
                'from_currency': usd,
                'to_currency': usd,
                'from_amount': Decimal('1000.00'),
                'to_amount': Decimal('1000.00'),
                'exchange_rate': Decimal('1.00'),
                'description': 'Test cash deposit - workflow testing',
                'status': Transaction.Status.COMPLETED,
                'delivery_method': Transaction.DeliveryMethod.CASH,
            }
            
            deposit_transaction = Transaction.objects.create(**deposit_data)
            
            results['cash_deposit'] = {
                'transaction_id': str(deposit_transaction.id),
                'amount': str(deposit_transaction.from_amount),
                'status': 'PASS',
                'details': f'Deposit transaction created: {deposit_transaction.reference_number}'
            }
            
            print(f"   ✅ Cash deposit: ${deposit_transaction.from_amount} - {deposit_transaction.reference_number}")
            
        except Exception as e:
            results['cash_deposit'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Cash deposit error: {e}")
        
        # Transaction 2: Cash Withdrawal
        print("3.2 Testing Cash Withdrawal Transaction...")
        try:
            withdrawal_data = {
                'transaction_type': 'WITHDRAWAL',
                'customer': self.test_customer,
                'location': location,
                'from_currency': usd,
                'to_currency': usd,
                'from_amount': Decimal('300.00'),
                'to_amount': Decimal('300.00'),
                'exchange_rate': Decimal('1.00'),
                'description': 'Test cash withdrawal - workflow testing',
                'status': Transaction.Status.COMPLETED,
                'delivery_method': Transaction.DeliveryMethod.CASH,
            }
            
            withdrawal_transaction = Transaction.objects.create(**withdrawal_data)
            
            results['cash_withdrawal'] = {
                'transaction_id': str(withdrawal_transaction.id),
                'amount': str(withdrawal_transaction.from_amount),
                'status': 'PASS',
                'details': f'Withdrawal transaction created: {withdrawal_transaction.reference_number}'
            }
            
            print(f"   ✅ Cash withdrawal: ${withdrawal_transaction.from_amount} - {withdrawal_transaction.reference_number}")
            
        except Exception as e:
            results['cash_withdrawal'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Cash withdrawal error: {e}")
        
        # Transaction 3: Internal Transfer
        print("3.3 Testing Internal Transfer Transaction...")
        try:
            transfer_data = {
                'transaction_type': 'TRANSFER',
                'customer': self.test_customer,
                'location': location,
                'from_currency': usd,
                'to_currency': usd,
                'from_amount': Decimal('200.00'),
                'to_amount': Decimal('200.00'),
                'exchange_rate': Decimal('1.00'),
                'description': f'Internal transfer to {self.test_customer2.get_display_name()}',
                'status': Transaction.Status.COMPLETED,
                'delivery_method': Transaction.DeliveryMethod.INTERNAL_TRANSFER,
                'notes': f'Transfer to customer: {self.test_customer2.customer_code}'
            }
            
            transfer_transaction = Transaction.objects.create(**transfer_data)
            
            results['internal_transfer'] = {
                'transaction_id': str(transfer_transaction.id),
                'amount': str(transfer_transaction.from_amount),
                'status': 'PASS',
                'details': f'Internal transfer created: {transfer_transaction.reference_number}'
            }
            
            print(f"   ✅ Internal transfer: ${transfer_transaction.from_amount} - {transfer_transaction.reference_number}")
            
        except Exception as e:
            results['internal_transfer'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ Internal transfer error: {e}")
        
        # Transaction 4: External Bank Transfer
        print("3.4 Testing External Bank Transfer...")
        try:
            bank_transfer_data = {
                'transaction_type': 'BANK_TRANSFER',
                'customer': self.test_customer,
                'location': location,
                'from_currency': usd,
                'to_currency': usd,
                'from_amount': Decimal('150.00'),
                'to_amount': Decimal('150.00'),
                'exchange_rate': Decimal('1.00'),
                'description': 'External bank transfer - workflow testing',
                'status': Transaction.Status.PENDING,
                'delivery_method': Transaction.DeliveryMethod.BANK_TRANSFER,
                'delivery_address': 'Test Bank, Account: *********, SWIFT: TESTUS33',
                'tracking_code': 'TRK' + str(int(time.time()))
            }
            
            bank_transfer_transaction = Transaction.objects.create(**bank_transfer_data)
            
            results['external_bank_transfer'] = {
                'transaction_id': str(bank_transfer_transaction.id),
                'amount': str(bank_transfer_transaction.from_amount),
                'status': 'PASS',
                'details': f'Bank transfer created: {bank_transfer_transaction.reference_number}'
            }
            
            print(f"   ✅ External bank transfer: ${bank_transfer_transaction.from_amount} - {bank_transfer_transaction.reference_number}")
            
        except Exception as e:
            results['external_bank_transfer'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ External bank transfer error: {e}")
        
        # Transaction 5: International Transfer with Currency Conversion
        print("3.5 Testing International Transfer with Currency Conversion...")
        try:
            aed = Currency.objects.get(code='AED')
            exchange_rate = Decimal('3.67')  # USD to AED rate
            
            intl_transfer_data = {
                'transaction_type': 'INTERNATIONAL_TRANSFER',
                'customer': self.test_customer,
                'location': location,
                'from_currency': usd,
                'to_currency': aed,
                'from_amount': Decimal('100.00'),
                'to_amount': Decimal('100.00') * exchange_rate,
                'exchange_rate': exchange_rate,
                'commission_amount': Decimal('5.00'),
                'commission_currency': usd,
                'description': 'International transfer with conversion - workflow testing',
                'status': Transaction.Status.PENDING,
                'delivery_method': Transaction.DeliveryMethod.INTERNATIONAL_TRANSFER,
                'delivery_address': 'Dubai Bank, UAE, Account: *********',
                'tracking_code': 'INTL' + str(int(time.time()))
            }
            
            intl_transfer_transaction = Transaction.objects.create(**intl_transfer_data)
            
            results['international_transfer'] = {
                'transaction_id': str(intl_transfer_transaction.id),
                'from_amount': str(intl_transfer_transaction.from_amount),
                'to_amount': str(intl_transfer_transaction.to_amount),
                'exchange_rate': str(intl_transfer_transaction.exchange_rate),
                'commission': str(intl_transfer_transaction.commission_amount),
                'status': 'PASS',
                'details': f'International transfer created: {intl_transfer_transaction.reference_number}'
            }
            
            print(f"   ✅ International transfer: ${intl_transfer_transaction.from_amount} → {aed.symbol}{intl_transfer_transaction.to_amount}")
            print(f"      Exchange rate: {exchange_rate}, Commission: ${intl_transfer_transaction.commission_amount}")
            
        except Exception as e:
            results['international_transfer'] = {'status': 'ERROR', 'error': str(e)}
            print(f"   ❌ International transfer error: {e}")
        
        self.test_results['phase3_transactions'] = results
        
        # Summary
        passed = sum(1 for r in results.values() if r.get('status') == 'PASS')
        total = len(results)
        print(f"\n📊 Phase 3 Summary: {passed}/{total} transaction tests passed")
        
        return passed == total
    
    def run_complete_workflow_test(self):
        """Run the complete workflow test."""
        print("🚀 ARENA DOVIZ COMPLETE WORKFLOW TEST")
        print("="*60)
        
        start_time = datetime.now()
        
        # Setup
        if not self.setup_test_data():
            print("❌ Test data setup failed. Aborting.")
            return False
        
        if not self.test_server_connectivity():
            print("❌ Server connectivity failed. Aborting.")
            return False
        
        # Execute phases
        phase_results = []
        
        phase_results.append(("Phase 1: Dropdown Verification", self.phase1_dropdown_verification()))
        phase_results.append(("Phase 2: Balance Verification", self.phase2_balance_verification()))
        phase_results.append(("Phase 3: Transaction Workflow", self.phase3_transaction_workflow()))
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        passed_phases = sum(1 for _, result in phase_results if result)
        total_phases = len(phase_results)
        
        print("\n" + "="*60)
        print("WORKFLOW TEST SUMMARY")
        print("="*60)
        
        for phase_name, result in phase_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {phase_name}")
        
        print(f"\n📊 Overall Results:")
        print(f"   Phases Passed: {passed_phases}/{total_phases}")
        print(f"   Success Rate: {passed_phases/total_phases:.1%}")
        print(f"   Duration: {duration}")
        
        self.test_results['summary'] = {
            'phases_passed': passed_phases,
            'total_phases': total_phases,
            'success_rate': passed_phases/total_phases,
            'duration': str(duration),
            'timestamp': end_time.isoformat()
        }
        
        if passed_phases == total_phases:
            print("\n🎉 ALL WORKFLOW TESTS PASSED!")
            print("✅ Arena Doviz transaction system is fully functional")
        else:
            print(f"\n⚠️  {total_phases - passed_phases} phase(s) failed")
            print("❌ Review failures and fix issues before production")
        
        return passed_phases == total_phases


def main():
    """Main function."""
    tester = WorkflowTester()
    success = tester.run_complete_workflow_test()
    
    # Save results
    import json
    results_file = Path('workflow_test_results.json')
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(tester.test_results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

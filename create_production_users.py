#!/usr/bin/env python3
"""
Arena Doviz Production Users Creation Script

Creates comprehensive test users with proper roles and permissions.
Removes any hardcoded credentials and provides secure login information.

Usage:
    python create_production_users.py
"""

import os
import sys
import django
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone

User = get_user_model()

def create_production_users():
    """Create comprehensive production users with proper roles."""
    
    print("🚀 Creating Arena Doviz Production Users")
    print("=" * 50)
    
    # Define user roles and their details
    users_data = [
        {
            'username': 'admin_user',
            'email': '<EMAIL>',
            'password': 'Admin123!@#',
            'first_name': 'System',
            'last_name': 'Administrator',
            'role': User.Role.ADMIN,
            'is_superuser': True,
            'is_staff': True,
            'description': 'Full system access, can manage all aspects of the system'
        },
        {
            'username': 'accountant_user',
            'email': '<EMAIL>',
            'password': 'Account123!@#',
            'first_name': 'Senior',
            'last_name': 'Accountant',
            'role': User.Role.ACCOUNTANT,
            'is_superuser': False,
            'is_staff': True,
            'description': 'Can approve transactions, manage accounting, view reports'
        },
        {
            'username': 'branch_employee',
            'email': '<EMAIL>',
            'password': 'Branch123!@#',
            'first_name': 'Branch',
            'last_name': 'Employee',
            'role': User.Role.BRANCH_EMPLOYEE,
            'is_superuser': False,
            'is_staff': False,
            'description': 'Can create and manage transactions for their location'
        },
        {
            'username': 'viewer_user',
            'email': '<EMAIL>',
            'password': 'Viewer123!@#',
            'first_name': 'Report',
            'last_name': 'Viewer',
            'role': User.Role.VIEWER,
            'is_superuser': False,
            'is_staff': False,
            'description': 'Read-only access to reports and transaction data'
        },
        {
            'username': 'courier_user',
            'email': '<EMAIL>',
            'password': 'Courier123!@#',
            'first_name': 'Delivery',
            'last_name': 'Courier',
            'role': User.Role.COURIER,
            'is_superuser': False,
            'is_staff': False,
            'description': 'Can handle deposit/withdrawal transactions with courier selection'
        }
    ]
    
    created_users = []
    updated_users = []
    
    try:
        with transaction.atomic():
            for user_data in users_data:
                username = user_data['username']
                password = user_data.pop('password')  # Remove password from data dict
                description = user_data.pop('description')  # Remove description
                
                # Check if user already exists
                user, created = User.objects.get_or_create(
                    username=username,
                    defaults=user_data
                )
                
                if created:
                    user.set_password(password)
                    user.save()
                    created_users.append((user, password, description))
                    print(f"✅ Created new user: {username}")
                else:
                    # Update existing user
                    for field, value in user_data.items():
                        setattr(user, field, value)
                    user.set_password(password)
                    user.save()
                    updated_users.append((user, password, description))
                    print(f"🔄 Updated existing user: {username}")
        
        # Display comprehensive user information
        print("\n" + "=" * 60)
        print("📋 ARENA DOVIZ PRODUCTION LOGIN CREDENTIALS")
        print("=" * 60)
        
        all_users = created_users + updated_users
        
        for user, password, description in all_users:
            print(f"\n🔐 {user.role.upper()} USER")
            print("-" * 30)
            print(f"Username: {user.username}")
            print(f"Password: {password}")
            print(f"Email: {user.email}")
            print(f"Full Name: {user.get_full_name()}")
            print(f"Role: {user.get_role_display()}")
            print(f"Description: {description}")
            
            # Show permissions
            print("Permissions:")
            permissions = {
                'Approve Transactions': user.can_approve_transactions(),
                'Manage Transactions': user.can_manage_transactions(),
                'View Reports': user.can_view_reports(),
                'Manage Users': user.can_manage_users(),
                'System Settings': user.can_manage_system_settings(),
            }
            
            for perm_name, has_perm in permissions.items():
                status = "✅" if has_perm else "❌"
                print(f"  {status} {perm_name}")
        
        print("\n" + "=" * 60)
        print("🎯 RECOMMENDED ADMIN CREDENTIALS FOR FIRST LOGIN")
        print("=" * 60)
        admin_data = next((data for data in all_users if data[0].role == User.Role.ADMIN), None)
        if admin_data:
            admin_user, admin_password, _ = admin_data
            print(f"Username: {admin_user.username}")
            print(f"Password: {admin_password}")
            print(f"URL: http://localhost:8000/accounts/login/")
        
        print("\n" + "=" * 60)
        print("📝 SECURITY NOTES")
        print("=" * 60)
        print("1. Change all passwords after first login")
        print("2. These are temporary credentials for initial setup")
        print("3. Use strong, unique passwords in production")
        print("4. Enable two-factor authentication if available")
        print("5. Regularly review user permissions")
        
        print(f"\n✅ Successfully processed {len(all_users)} users")
        print(f"   - Created: {len(created_users)}")
        print(f"   - Updated: {len(updated_users)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating users: {e}")
        return False

def test_user_login():
    """Test that users can login with the created credentials."""
    print("\n🧪 Testing User Login Capabilities")
    print("-" * 40)
    
    from django.test import Client
    from django.contrib.auth import authenticate
    
    test_credentials = [
        ('admin_user', 'Admin123!@#'),
        ('accountant_user', 'Account123!@#'),
    ]
    
    for username, password in test_credentials:
        try:
            # Test authentication
            user = authenticate(username=username, password=password)
            if user:
                print(f"✅ {username}: Authentication successful")
                
                # Test login via client
                client = Client()
                login_success = client.login(username=username, password=password)
                if login_success:
                    print(f"   ✅ Login test successful")
                else:
                    print(f"   ❌ Login test failed")
            else:
                print(f"❌ {username}: Authentication failed")
                
        except Exception as e:
            print(f"❌ {username}: Error - {e}")

def main():
    """Main function."""
    success = create_production_users()
    
    if success:
        test_user_login()
        
        print("\n🎉 User creation completed successfully!")
        print("\nNext steps:")
        print("1. Start the production server: python run_server.bat")
        print("2. Access the system at: http://localhost:8000")
        print("3. Login with the admin credentials shown above")
        print("4. Test the transaction approval functionality")
        
        return True
    else:
        print("\n❌ User creation failed!")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

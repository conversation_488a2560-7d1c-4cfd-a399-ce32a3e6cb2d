#!/usr/bin/env python3
"""
Create Test Transaction for Approval Testing

This script creates a test transaction that can be used to test the approval functionality.
"""

import os
import sys
import django
from pathlib import Path
from decimal import Decimal

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone

from apps.transactions.models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency

User = get_user_model()

def create_test_transaction():
    """Create a test transaction for approval testing."""
    
    print("🧪 Creating Test Transaction for Approval Testing")
    print("=" * 50)
    
    try:
        # Get required objects
        customer = Customer.objects.first()
        location = Location.objects.first()
        usd_currency = Currency.objects.filter(code='USD').first()
        aed_currency = Currency.objects.filter(code='AED').first()
        exchange_type = TransactionType.objects.filter(code='EXCHANGE').first()
        branch_user = User.objects.filter(role=User.Role.BRANCH_EMPLOYEE).first()
        
        if not all([customer, location, usd_currency, aed_currency, exchange_type, branch_user]):
            print("❌ Missing required data:")
            print(f"   Customer: {'✅' if customer else '❌'}")
            print(f"   Location: {'✅' if location else '❌'}")
            print(f"   USD Currency: {'✅' if usd_currency else '❌'}")
            print(f"   AED Currency: {'✅' if aed_currency else '❌'}")
            print(f"   Exchange Type: {'✅' if exchange_type else '❌'}")
            print(f"   Branch User: {'✅' if branch_user else '❌'}")
            return None
        
        # Create test transaction
        with transaction.atomic():
            test_transaction = Transaction.objects.create(
                transaction_type=exchange_type,
                customer=customer,
                location=location,
                from_currency=usd_currency,
                to_currency=aed_currency,
                from_amount=Decimal('100.00'),
                to_amount=Decimal('367.00'),
                exchange_rate=Decimal('3.67'),
                commission_amount=Decimal('2.00'),
                commission_currency=usd_currency,
                status=Transaction.Status.PENDING,
                description='Test transaction for approval testing',
                notes='Created by test script for approval functionality testing',
                created_by=branch_user,
                delivery_method=Transaction.DeliveryMethod.IN_PERSON,
            )
        
        print(f"✅ Test transaction created successfully!")
        print(f"   Transaction ID: {test_transaction.id}")
        print(f"   Transaction Number: {test_transaction.transaction_number}")
        print(f"   Status: {test_transaction.status}")
        print(f"   Customer: {test_transaction.customer}")
        print(f"   Amount: {test_transaction.from_amount} {test_transaction.from_currency.code} → {test_transaction.to_amount} {test_transaction.to_currency.code}")
        print(f"   Can be approved: {test_transaction.can_be_approved()}")
        
        # Test approval API endpoint URL
        api_url = f"/api/v1/transactions/transactions/{test_transaction.id}/approve/"
        print(f"   API Endpoint: {api_url}")
        
        return test_transaction
        
    except Exception as e:
        print(f"❌ Error creating test transaction: {e}")
        return None

def test_approval_api(test_transaction):
    """Test the approval API with the created transaction."""
    
    print("\n🔧 Testing Approval API")
    print("-" * 30)
    
    try:
        from django.test import Client
        
        # Get admin user
        admin_user = User.objects.filter(role=User.Role.ADMIN).first()
        if not admin_user:
            print("❌ No admin user found")
            return False
        
        # Create test client
        client = Client()
        client.force_login(admin_user)
        
        # Test the approval API
        url = f'/api/v1/transactions/transactions/{test_transaction.id}/approve/'
        response = client.post(url, content_type='application/json')
        
        print(f"   API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Approval API test successful!")
            
            # Refresh transaction from database
            test_transaction.refresh_from_db()
            print(f"   New status: {test_transaction.status}")
            print(f"   Approved by: {test_transaction.approved_by}")
            print(f"   Approved at: {test_transaction.approved_at}")
            
            return True
        else:
            print(f"❌ Approval API test failed!")
            print(f"   Response content: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing approval API: {e}")
        return False

def main():
    """Main function."""
    
    # Create test transaction
    test_transaction = create_test_transaction()
    
    if test_transaction:
        # Test approval API
        approval_success = test_approval_api(test_transaction)
        
        if approval_success:
            print("\n🎉 All tests passed!")
            print("\nThe transaction approval functionality is working correctly.")
            print("You can now:")
            print("1. Start the production server")
            print("2. Login with admin credentials")
            print("3. Test the transaction approval in the web interface")
        else:
            print("\n⚠️  Approval API test failed")
            print("Check the server logs for more details")
        
        return approval_success
    else:
        print("\n❌ Failed to create test transaction")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

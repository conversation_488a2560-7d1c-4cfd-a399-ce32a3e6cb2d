#!/usr/bin/env python3
"""
Arena Doviz Critical Production Issues Debug Script

This script identifies and fixes:
1. Transaction approval API HTTP 500 errors
2. Static file MIME type issues
3. Authentication and user management problems
4. Creates comprehensive test users with proper roles

Usage:
    python debug_critical_production_issues.py
"""

import os
import sys
import django
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group
from django.db import transaction
from django.test import Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import json
import logging

# Import models
from apps.transactions.models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency

User = get_user_model()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, message=""):
    """Print test result with emoji."""
    emoji = "✅" if success else "❌"
    print(f"{emoji} {test_name}: {'SUCCESS' if success else 'FAILED'}")
    if message:
        print(f"   {message}")

def debug_transaction_approval_api():
    """Debug Issue 1: Transaction Approval API HTTP 500 Error"""
    print_header("Debugging Transaction Approval API")
    
    try:
        # Find the specific transaction
        transaction_id = 'b231ba81-5931-49f0-8b13-e6fb719537d1'
        
        try:
            transaction_obj = Transaction.objects.get(id=transaction_id)
            print_result("Transaction Found", True, f"Transaction: {transaction_obj.transaction_number}")
            
            # Check transaction status
            print(f"   Current Status: {transaction_obj.status}")
            print(f"   Can be approved: {transaction_obj.can_be_approved()}")
            
            # Check if there's a user who can approve
            admin_users = User.objects.filter(role=User.Role.ADMIN, is_active=True)
            if admin_users.exists():
                admin_user = admin_users.first()
                print(f"   Admin user found: {admin_user.username}")
                print(f"   Can approve transactions: {admin_user.can_approve_transactions()}")
                
                # Test the approval logic manually
                if transaction_obj.can_be_approved() and admin_user.can_approve_transactions():
                    print("   Testing approval logic...")
                    
                    # Create a test client
                    client = Client()
                    client.force_login(admin_user)
                    
                    # Test the API endpoint
                    url = f'/api/v1/transactions/transactions/{transaction_id}/approve/'
                    response = client.post(url, content_type='application/json')
                    
                    print(f"   API Response Status: {response.status_code}")
                    if response.status_code != 200:
                        print(f"   Response Content: {response.content.decode()}")
                        
                        # Try to identify the specific error
                        if response.status_code == 500:
                            print("   🔍 Investigating 500 error...")
                            
                            # Check for common issues
                            if not hasattr(transaction_obj, 'customer'):
                                print("   ❌ Missing customer relationship")
                            elif not transaction_obj.customer:
                                print("   ❌ Customer is None")
                            else:
                                print(f"   ✅ Customer: {transaction_obj.customer}")
                            
                            # Check for missing fields
                            required_fields = ['from_amount', 'transaction_number']
                            for field in required_fields:
                                if not hasattr(transaction_obj, field) or getattr(transaction_obj, field) is None:
                                    print(f"   ❌ Missing field: {field}")
                                else:
                                    print(f"   ✅ Field {field}: {getattr(transaction_obj, field)}")
                    
                    print_result("API Test", response.status_code == 200)
                else:
                    print_result("Approval Prerequisites", False, "Transaction cannot be approved or user lacks permission")
            else:
                print_result("Admin User", False, "No admin users found")
                
        except Transaction.DoesNotExist:
            print_result("Transaction Found", False, f"Transaction {transaction_id} not found")
            
            # List available transactions for testing
            transactions = Transaction.objects.filter(status__in=['draft', 'pending'])[:5]
            if transactions:
                print("   Available transactions for testing:")
                for txn in transactions:
                    print(f"   - {txn.id}: {txn.transaction_number} ({txn.status})")
            else:
                print("   No transactions available for testing")
        
        return True
        
    except Exception as e:
        print_result("Transaction Approval Debug", False, str(e))
        return False

def fix_static_file_mime_types():
    """Fix Issue 2: Static File MIME Type Problems"""
    print_header("Fixing Static File MIME Types")
    
    try:
        from django.conf import settings
        from django.contrib.staticfiles import finders
        from django.http import HttpResponse
        from django.test import Client
        
        # Test static file serving
        client = Client()
        
        # Test JavaScript file
        js_response = client.get('/static/js/arena-doviz.js')
        print(f"   JavaScript file status: {js_response.status_code}")
        
        if js_response.status_code == 200:
            content_type = js_response.get('Content-Type', '')
            print(f"   Content-Type: {content_type}")
            
            if 'javascript' in content_type or 'application/javascript' in content_type:
                print_result("JavaScript MIME Type", True)
            else:
                print_result("JavaScript MIME Type", False, f"Incorrect MIME type: {content_type}")
                
                # Fix by updating Django settings
                print("   🔧 Applying MIME type fix...")
                
                # Create a custom static files handler
                fix_content = '''
# Add to Django settings to fix MIME types
import mimetypes
mimetypes.add_type('application/javascript', '.js')
mimetypes.add_type('text/css', '.css')
'''
                print("   Add this to your Django settings:")
                print(fix_content)
        else:
            print_result("Static File Access", False, f"Cannot access static files: {js_response.status_code}")
        
        # Check if static files are collected
        static_root = getattr(settings, 'STATIC_ROOT', None)
        if static_root and Path(static_root).exists():
            js_file = Path(static_root) / 'js' / 'arena-doviz.js'
            if js_file.exists():
                print_result("Static Files Collected", True)
            else:
                print_result("Static Files Collected", False, "arena-doviz.js not found in static root")
        else:
            print_result("Static Root", False, "STATIC_ROOT not configured or doesn't exist")
        
        return True
        
    except Exception as e:
        print_result("Static File MIME Fix", False, str(e))
        return False

def create_comprehensive_test_users():
    """Fix Issue 3: Create comprehensive test users with proper roles"""
    print_header("Creating Comprehensive Test Users")
    
    try:
        # Define user roles and their details
        test_users = [
            {
                'username': 'admin_user',
                'email': '<EMAIL>',
                'password': 'Admin123!@#',
                'first_name': 'System',
                'last_name': 'Administrator',
                'role': User.Role.ADMIN,
                'is_superuser': True,
                'is_staff': True,
            },
            {
                'username': 'accountant_user',
                'email': '<EMAIL>',
                'password': 'Account123!@#',
                'first_name': 'Senior',
                'last_name': 'Accountant',
                'role': User.Role.ACCOUNTANT,
                'is_superuser': False,
                'is_staff': True,
            },
            {
                'username': 'branch_employee',
                'email': '<EMAIL>',
                'password': 'Branch123!@#',
                'first_name': 'Branch',
                'last_name': 'Employee',
                'role': User.Role.BRANCH_EMPLOYEE,
                'is_superuser': False,
                'is_staff': False,
            },
            {
                'username': 'viewer_user',
                'email': '<EMAIL>',
                'password': 'Viewer123!@#',
                'first_name': 'Report',
                'last_name': 'Viewer',
                'role': User.Role.VIEWER,
                'is_superuser': False,
                'is_staff': False,
            },
            {
                'username': 'courier_user',
                'email': '<EMAIL>',
                'password': 'Courier123!@#',
                'first_name': 'Delivery',
                'last_name': 'Courier',
                'role': User.Role.COURIER,
                'is_superuser': False,
                'is_staff': False,
            }
        ]
        
        created_users = []
        
        with transaction.atomic():
            for user_data in test_users:
                username = user_data['username']
                
                # Check if user already exists
                if User.objects.filter(username=username).exists():
                    user = User.objects.get(username=username)
                    print(f"   User {username} already exists, updating...")
                    
                    # Update existing user
                    for field, value in user_data.items():
                        if field != 'password':
                            setattr(user, field, value)
                    user.set_password(user_data['password'])
                    user.save()
                else:
                    # Create new user
                    password = user_data.pop('password')
                    user = User.objects.create_user(
                        password=password,
                        **user_data
                    )
                    print(f"   Created new user: {username}")
                
                created_users.append(user)
                print_result(f"User {username}", True, f"Role: {user.role}, Email: {user.email}")
        
        # Display login credentials
        print("\n📋 LOGIN CREDENTIALS:")
        print("=" * 50)
        for user_data in test_users:
            print(f"Role: {user_data['role'].title()}")
            print(f"Username: {user_data['username']}")
            print(f"Password: {user_data['password']}")
            print(f"Email: {user_data['email']}")
            print("-" * 30)
        
        print_result("Test Users Creation", True, f"Created/Updated {len(created_users)} users")
        return True, created_users
        
    except Exception as e:
        print_result("Test Users Creation", False, str(e))
        return False, []

def test_user_permissions():
    """Test user permissions and role-based access"""
    print_header("Testing User Permissions")
    
    try:
        # Test each role's permissions
        roles_to_test = [
            (User.Role.ADMIN, 'admin_user'),
            (User.Role.ACCOUNTANT, 'accountant_user'),
            (User.Role.BRANCH_EMPLOYEE, 'branch_employee'),
            (User.Role.VIEWER, 'viewer_user'),
            (User.Role.COURIER, 'courier_user'),
        ]
        
        for role, username in roles_to_test:
            try:
                user = User.objects.get(username=username)
                print(f"\n   Testing {role} permissions for {username}:")
                
                # Test specific permissions
                permissions = {
                    'can_approve_transactions': user.can_approve_transactions(),
                    'can_manage_transactions': user.can_manage_transactions(),
                    'can_view_reports': user.can_view_reports(),
                    'can_manage_users': user.can_manage_users(),
                    'can_manage_system_settings': user.can_manage_system_settings(),
                }
                
                for perm, has_perm in permissions.items():
                    status = "✅" if has_perm else "❌"
                    print(f"     {status} {perm}")
                
                print_result(f"{role} Permissions", True)
                
            except User.DoesNotExist:
                print_result(f"{role} User", False, f"User {username} not found")
        
        return True
        
    except Exception as e:
        print_result("Permission Testing", False, str(e))
        return False

def remove_hardcoded_credentials():
    """Remove hardcoded test credentials from login page"""
    print_header("Removing Hardcoded Credentials")
    
    try:
        # Check login template for hardcoded credentials
        login_template_path = Path('src/templates/accounts/login.html')
        
        if login_template_path.exists():
            with open(login_template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for common patterns of hardcoded credentials
            hardcoded_patterns = [
                'value="admin"',
                'value="password"',
                'placeholder="admin"',
                'placeholder="password"',
                'test_user',
                'demo_user',
            ]
            
            found_hardcoded = []
            for pattern in hardcoded_patterns:
                if pattern in content:
                    found_hardcoded.append(pattern)
            
            if found_hardcoded:
                print_result("Hardcoded Credentials Found", False, f"Found: {found_hardcoded}")
                print("   🔧 Manual removal required in login template")
            else:
                print_result("Hardcoded Credentials Check", True, "No hardcoded credentials found")
        else:
            print_result("Login Template", False, "Login template not found")
        
        return True
        
    except Exception as e:
        print_result("Credential Removal", False, str(e))
        return False

def main():
    """Main function to run all debug and fix operations"""
    print_header("Arena Doviz Critical Production Issues Debug")
    
    results = []
    
    # Run all debug and fix operations
    operations = [
        ("Transaction Approval API Debug", debug_transaction_approval_api),
        ("Static File MIME Types Fix", fix_static_file_mime_types),
        ("Test Users Creation", lambda: create_comprehensive_test_users()[0]),
        ("User Permissions Test", test_user_permissions),
        ("Hardcoded Credentials Removal", remove_hardcoded_credentials),
    ]
    
    for operation_name, operation_func in operations:
        print(f"\n🔄 Running: {operation_name}")
        try:
            result = operation_func()
            results.append((operation_name, result))
        except Exception as e:
            print_result(operation_name, False, str(e))
            results.append((operation_name, False))
    
    # Summary
    print_header("Debug Summary")
    successful = sum(1 for _, result in results if result)
    total = len(results)
    
    for operation_name, result in results:
        print_result(operation_name, result)
    
    print(f"\n📊 Overall Result: {successful}/{total} operations successful")
    
    if successful == total:
        print("🎉 All critical issues have been addressed!")
    else:
        print("⚠️  Some issues require manual attention.")
    
    return successful == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

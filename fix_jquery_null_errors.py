#!/usr/bin/env python3
"""
Fix jQuery null value errors in Arena Doviz

This script identifies and fixes jQuery null value errors by:
1. Adding proper DOM ready checks
2. Implementing defensive programming for element access
3. Fixing timing issues with form element access
4. Adding null checks before accessing .value property

Usage:
    python fix_jquery_null_errors.py
"""

import os
import sys
import re
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))


class JQueryErrorFixer:
    """Fix jQuery null value errors systematically."""
    
    def __init__(self):
        self.src_path = src_path
        self.static_js_path = self.src_path / 'static' / 'js'
        self.staticfiles_js_path = self.src_path / 'staticfiles' / 'js'
        self.fixes_applied = []
        self.errors_found = []
    
    def analyze_javascript_files(self):
        """Analyze JavaScript files for potential null access issues."""
        print("🔍 Analyzing JavaScript files for null access issues...")
        
        js_files = []
        
        # Find all JavaScript files
        for js_dir in [self.static_js_path, self.staticfiles_js_path]:
            if js_dir.exists():
                js_files.extend(js_dir.rglob('*.js'))
        
        potential_issues = []
        
        for js_file in js_files:
            try:
                content = js_file.read_text(encoding='utf-8')
                issues = self.find_potential_null_access(content, js_file)
                potential_issues.extend(issues)
            except Exception as e:
                print(f"⚠️  Error reading {js_file}: {e}")
        
        return potential_issues
    
    def find_potential_null_access(self, content, file_path):
        """Find potential null access issues in JavaScript content."""
        issues = []
        lines = content.split('\n')
        
        # Patterns that might cause null access errors
        patterns = [
            # Direct element access without null check
            (r'document\.getElementById\([^)]+\)\.value', 'getElementById without null check'),
            (r'document\.querySelector\([^)]+\)\.value', 'querySelector without null check'),
            (r'document\.getElementsByName\([^)]+\)\[0\]\.value', 'getElementsByName without null check'),
            
            # jQuery element access without existence check
            (r'\$\([^)]+\)\.val\(\)', 'jQuery val() without existence check'),
            (r'\$\([^)]+\)\.text\(\)', 'jQuery text() without existence check'),
            (r'\$\([^)]+\)\.html\(\)', 'jQuery html() without existence check'),
            
            # Form element access patterns
            (r'\.elements\[[^]]+\]\.value', 'Form elements access without null check'),
            (r'\.form\.[^.]+\.value', 'Form property access without null check'),
            
            # Event handler issues
            (r'\.addEventListener\([^,]+,\s*function\([^)]*\)\s*{[^}]*\.value', 'Event handler with potential null access'),
        ]
        
        for line_num, line in enumerate(lines, 1):
            for pattern, description in patterns:
                if re.search(pattern, line):
                    issues.append({
                        'file': file_path,
                        'line': line_num,
                        'content': line.strip(),
                        'issue': description,
                        'pattern': pattern
                    })
        
        return issues
    
    def fix_transaction_form_issues(self):
        """Fix specific transaction form issues."""
        print("🔧 Fixing transaction form issues...")
        
        # Fix common.js transaction utilities
        common_js_files = [
            self.static_js_path / 'transactions' / 'common.js',
            self.staticfiles_js_path / 'transactions' / 'common.js'
        ]
        
        for common_js in common_js_files:
            if common_js.exists():
                self.fix_common_js_file(common_js)
    
    def fix_common_js_file(self, file_path):
        """Fix common.js file with defensive programming."""
        print(f"🔧 Fixing {file_path}")
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Add defensive element access methods at the beginning of the class
            defensive_methods = '''
    /**
     * Safely get element value with null checking
     */
    safeGetElementValue(selector) {
        try {
            const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
            return element && element.value !== undefined ? element.value : '';
        } catch (error) {
            console.warn('Error getting element value:', selector, error);
            return '';
        }
    }

    /**
     * Safely set element value with null checking
     */
    safeSetElementValue(selector, value) {
        try {
            const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
            if (element && element.value !== undefined) {
                element.value = value;
                return true;
            }
            return false;
        } catch (error) {
            console.warn('Error setting element value:', selector, error);
            return false;
        }
    }

    /**
     * Safely get jQuery element value
     */
    safeGetJQueryValue(selector) {
        try {
            const $element = $(selector);
            return $element.length > 0 ? $element.val() : '';
        } catch (error) {
            console.warn('Error getting jQuery value:', selector, error);
            return '';
        }
    }

    /**
     * Safely set jQuery element value
     */
    safeSetJQueryValue(selector, value) {
        try {
            const $element = $(selector);
            if ($element.length > 0) {
                $element.val(value);
                return true;
            }
            return false;
        } catch (error) {
            console.warn('Error setting jQuery value:', selector, error);
            return false;
        }
    }

    /**
     * Wait for element to exist before accessing
     */
    async waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            
            checkElement();
        });
    }

'''
            
            # Insert defensive methods after class declaration
            class_pattern = r'(class\s+\w+\s*{)'
            if re.search(class_pattern, content):
                content = re.sub(class_pattern, r'\1' + defensive_methods, content, count=1)
            else:
                # If no class found, add to beginning of file
                content = defensive_methods + '\n' + content
            
            # Fix specific problematic patterns
            fixes = [
                # Fix direct getElementById access
                (r'document\.getElementById\(([^)]+)\)\.value', 
                 r'this.safeGetElementValue(document.getElementById(\1))'),
                
                # Fix querySelector access
                (r'document\.querySelector\(([^)]+)\)\.value', 
                 r'this.safeGetElementValue(\1)'),
                
                # Fix form element access in submitTransaction
                (r'const\s+formData\s+=\s+new\s+FormData\(form\);', 
                 '''const formData = new FormData(form);
        
        // Add defensive checks for form elements
        if (!form || !form.elements) {
            console.error('Invalid form or form elements not available');
            return;
        }'''),
                
                # Fix jQuery val() access without checking existence
                (r'\$\(([^)]+)\)\.val\(\)(?!\s*\|\|)', 
                 r'this.safeGetJQueryValue(\1)'),
            ]
            
            for pattern, replacement in fixes:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    self.fixes_applied.append(f"Fixed {pattern} in {file_path}")
            
            # Write back the fixed content
            file_path.write_text(content, encoding='utf-8')
            print(f"✅ Fixed {file_path}")
            
        except Exception as e:
            print(f"❌ Error fixing {file_path}: {e}")
            self.errors_found.append(f"Error fixing {file_path}: {e}")
    
    def fix_form_initialization_timing(self):
        """Fix form initialization timing issues."""
        print("⏰ Fixing form initialization timing issues...")
        
        # Fix transaction form initialization
        transaction_js_files = [
            self.static_js_path / 'transactions' / 'exchange.js',
            self.static_js_path / 'transactions' / 'deposit.js',
            self.static_js_path / 'transactions' / 'transfer.js',
            self.staticfiles_js_path / 'transactions' / 'exchange.js',
            self.staticfiles_js_path / 'transactions' / 'deposit.js',
            self.staticfiles_js_path / 'transactions' / 'transfer.js',
        ]
        
        for js_file in transaction_js_files:
            if js_file.exists():
                self.fix_transaction_js_timing(js_file)
    
    def fix_transaction_js_timing(self, file_path):
        """Fix timing issues in transaction JavaScript files."""
        print(f"⏰ Fixing timing in {file_path}")
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Wrap immediate execution in DOM ready
            if 'document.addEventListener(\'DOMContentLoaded\'' not in content:
                # Find class instantiation or immediate execution
                instantiation_pattern = r'(new\s+\w+Exchange\w*\(\)|new\s+\w+Deposit\w*\(\)|new\s+\w+Transfer\w*\(\))'
                
                if re.search(instantiation_pattern, content):
                    # Wrap in DOM ready
                    dom_ready_wrapper = '''
document.addEventListener('DOMContentLoaded', function() {
    // Wait for jQuery and ArenaDoviz to be available
    const waitForDependencies = () => {
        if (typeof $ !== 'undefined' && typeof ArenaDoviz !== 'undefined') {
            // Initialize transaction form
            try {
'''
                    
                    dom_ready_closer = '''
            } catch (error) {
                console.error('Error initializing transaction form:', error);
            }
        } else {
            // Retry after 100ms
            setTimeout(waitForDependencies, 100);
        }
    };
    
    waitForDependencies();
});'''
                    
                    # Find the instantiation and wrap it
                    content = re.sub(
                        instantiation_pattern,
                        dom_ready_wrapper + r'\1' + dom_ready_closer,
                        content,
                        count=1
                    )
                    
                    self.fixes_applied.append(f"Added DOM ready wrapper to {file_path}")
            
            # Write back the fixed content
            file_path.write_text(content, encoding='utf-8')
            print(f"✅ Fixed timing in {file_path}")
            
        except Exception as e:
            print(f"❌ Error fixing timing in {file_path}: {e}")
            self.errors_found.append(f"Error fixing timing in {file_path}: {e}")
    
    def add_global_error_handling(self):
        """Add global error handling for null access."""
        print("🛡️  Adding global error handling...")
        
        # Update arena-doviz.js with better error handling
        arena_js_files = [
            self.static_js_path / 'arena-doviz.js',
            self.staticfiles_js_path / 'arena-doviz.js'
        ]
        
        for arena_js in arena_js_files:
            if arena_js.exists():
                self.add_error_handling_to_arena_js(arena_js)
    
    def add_error_handling_to_arena_js(self, file_path):
        """Add error handling to main arena-doviz.js file."""
        print(f"🛡️  Adding error handling to {file_path}")
        
        try:
            content = file_path.read_text(encoding='utf-8')
            
            # Add global utilities for safe element access
            global_utilities = '''
        /**
         * Global utilities for safe DOM access
         */
        dom: {
            /**
             * Safely get element by ID
             */
            safeGetById: function(id) {
                try {
                    return document.getElementById(id);
                } catch (error) {
                    console.warn('Error getting element by ID:', id, error);
                    return null;
                }
            },

            /**
             * Safely query selector
             */
            safeQuery: function(selector) {
                try {
                    return document.querySelector(selector);
                } catch (error) {
                    console.warn('Error querying selector:', selector, error);
                    return null;
                }
            },

            /**
             * Safely get element value
             */
            safeGetValue: function(element) {
                try {
                    if (typeof element === 'string') {
                        element = this.safeQuery(element);
                    }
                    return element && element.value !== undefined ? element.value : '';
                } catch (error) {
                    console.warn('Error getting element value:', element, error);
                    return '';
                }
            },

            /**
             * Safely set element value
             */
            safeSetValue: function(element, value) {
                try {
                    if (typeof element === 'string') {
                        element = this.safeQuery(element);
                    }
                    if (element && element.value !== undefined) {
                        element.value = value;
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.warn('Error setting element value:', element, error);
                    return false;
                }
            }
        },

'''
            
            # Insert utilities into ArenaDoviz object
            utils_pattern = r'(utils:\s*{)'
            if re.search(utils_pattern, content):
                content = re.sub(utils_pattern, r'\1' + global_utilities, content, count=1)
            else:
                # Add before the closing of ArenaDoviz object
                closing_pattern = r'(\s*}\s*;\s*$)'
                content = re.sub(closing_pattern, global_utilities + r'\1', content)
            
            # Write back the fixed content
            file_path.write_text(content, encoding='utf-8')
            print(f"✅ Added error handling to {file_path}")
            
        except Exception as e:
            print(f"❌ Error adding error handling to {file_path}: {e}")
            self.errors_found.append(f"Error adding error handling to {file_path}: {e}")
    
    def run_fixes(self):
        """Run all fixes."""
        print("🚀 Starting jQuery null error fixes...")
        print("=" * 50)
        
        # Analyze issues first
        issues = self.analyze_javascript_files()
        
        if issues:
            print(f"📊 Found {len(issues)} potential issues:")
            for issue in issues[:10]:  # Show first 10
                print(f"   {issue['file'].name}:{issue['line']} - {issue['issue']}")
            if len(issues) > 10:
                print(f"   ... and {len(issues) - 10} more")
        
        # Apply fixes
        self.fix_transaction_form_issues()
        self.fix_form_initialization_timing()
        self.add_global_error_handling()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Fix Summary")
        print("=" * 50)
        
        print(f"✅ Fixes Applied: {len(self.fixes_applied)}")
        for fix in self.fixes_applied:
            print(f"   {fix}")
        
        if self.errors_found:
            print(f"\n❌ Errors Encountered: {len(self.errors_found)}")
            for error in self.errors_found:
                print(f"   {error}")
        
        if len(self.fixes_applied) > 0:
            print("\n🎉 jQuery null error fixes completed successfully!")
            print("💡 Recommendation: Collect static files and test the application")
        else:
            print("\n⚠️  No fixes were applied. Manual review may be needed.")
        
        return len(self.errors_found) == 0


def main():
    """Main function."""
    print("🔧 Arena Doviz jQuery Null Error Fixer")
    print("=" * 50)
    
    fixer = JQueryErrorFixer()
    success = fixer.run_fixes()
    
    if success:
        print("\n✅ All fixes completed successfully!")
        print("Next steps:")
        print("1. Run: python manage.py collectstatic --noinput")
        print("2. Test the application in browser")
        print("3. Check browser console for any remaining errors")
    else:
        print("\n❌ Some errors occurred during fixing")
        print("Please review the errors above and fix manually if needed")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

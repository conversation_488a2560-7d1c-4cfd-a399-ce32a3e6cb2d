#!/usr/bin/env python3
"""
Fix Production Issues Script

This script applies all the fixes for:
1. CORS policy issues
2. Cross-Origin-Opener-Policy header warnings
3. Test credentials removal
4. Network access configuration

Usage:
    python fix_production_issues.py
"""

import os
import sys
import django
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.core.management import call_command
from django.conf import settings

def collect_static_files():
    """Collect static files for production."""
    print("📦 Collecting static files...")
    try:
        call_command('collectstatic', '--noinput', verbosity=0)
        print("   ✅ Static files collected successfully")
        return True
    except Exception as e:
        print(f"   ❌ Error collecting static files: {e}")
        return False

def verify_cors_settings():
    """Verify CORS settings are properly configured."""
    print("🌐 Verifying CORS settings...")
    
    try:
        # Check if CORS is enabled
        cors_allow_all = getattr(settings, 'CORS_ALLOW_ALL_ORIGINS', False)
        cors_allow_credentials = getattr(settings, 'CORS_ALLOW_CREDENTIALS', False)
        allowed_hosts = getattr(settings, 'ALLOWED_HOSTS', [])
        
        print(f"   CORS_ALLOW_ALL_ORIGINS: {cors_allow_all}")
        print(f"   CORS_ALLOW_CREDENTIALS: {cors_allow_credentials}")
        print(f"   ALLOWED_HOSTS: {allowed_hosts}")
        
        if cors_allow_all and cors_allow_credentials:
            print("   ✅ CORS settings configured for testing")
            return True
        else:
            print("   ⚠️  CORS settings may need adjustment")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking CORS settings: {e}")
        return False

def verify_middleware():
    """Verify middleware configuration."""
    print("🔧 Verifying middleware configuration...")
    
    try:
        middleware = getattr(settings, 'MIDDLEWARE', [])
        
        # Check for required middleware
        required_middleware = [
            'apps.core.middleware.CrossOriginPolicyMiddleware',
            'corsheaders.middleware.CorsMiddleware',
        ]
        
        for mw in required_middleware:
            if mw in middleware:
                print(f"   ✅ {mw} - Found")
            else:
                print(f"   ❌ {mw} - Missing")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error checking middleware: {e}")
        return False

def test_server_configuration():
    """Test server configuration."""
    print("🧪 Testing server configuration...")
    
    try:
        from django.test import Client
        from django.contrib.auth import get_user_model
        
        User = get_user_model()
        
        # Check if admin user exists
        admin_user = User.objects.filter(role=User.Role.ADMIN).first()
        if admin_user:
            print(f"   ✅ Admin user found: {admin_user.username}")
        else:
            print("   ❌ No admin user found")
            return False
        
        # Test client creation
        client = Client()
        response = client.get('/accounts/login/')
        
        if response.status_code == 200:
            print("   ✅ Login page accessible")
        else:
            print(f"   ❌ Login page error: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error testing server configuration: {e}")
        return False

def display_network_access_info():
    """Display network access information."""
    print("\n🌍 NETWORK ACCESS CONFIGURATION")
    print("=" * 50)
    
    # Get local IP addresses
    import socket
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    
    print(f"Computer Name: {hostname}")
    print(f"Local IP Address: {local_ip}")
    print("\n📱 ACCESS FROM OTHER DEVICES:")
    print(f"   Local Network: http://{local_ip}:8000")
    print(f"   Localhost: http://localhost:8000")
    print(f"   127.0.0.1: http://127.0.0.1:8000")
    
    print("\n🔐 LOGIN CREDENTIALS:")
    print("   Username: admin_user")
    print("   Password: Admin123!@#")
    
    print("\n⚠️  FIREWALL NOTES:")
    print("   - Ensure Windows Firewall allows port 8000")
    print("   - Run server with: python manage.py runserver 0.0.0.0:8000")
    print("   - Use 0.0.0.0 to bind to all network interfaces")

def main():
    """Main function to run all fixes and verifications."""
    print("🚀 Arena Doviz Production Issues Fix")
    print("=" * 50)
    
    operations = [
        ("Static Files Collection", collect_static_files),
        ("CORS Settings Verification", verify_cors_settings),
        ("Middleware Verification", verify_middleware),
        ("Server Configuration Test", test_server_configuration),
    ]
    
    results = []
    
    for operation_name, operation_func in operations:
        print(f"\n🔄 {operation_name}")
        print("-" * 30)
        
        try:
            result = operation_func()
            results.append((operation_name, result))
        except Exception as e:
            print(f"❌ {operation_name} failed: {e}")
            results.append((operation_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Fix Summary")
    print("=" * 50)
    
    successful = sum(1 for _, result in results if result)
    total = len(results)
    
    for operation_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {operation_name}")
    
    print(f"\n📈 Overall Result: {successful}/{total} operations successful")
    
    # Display network access info
    display_network_access_info()
    
    if successful == total:
        print("\n🎉 All fixes applied successfully!")
        print("\n🚀 READY TO START SERVER:")
        print("1. Run: python run_server.bat")
        print("2. Or: cd src && python manage.py runserver 0.0.0.0:8000")
        print("3. Access from any device on your network")
        print("4. No more CORS or Cross-Origin-Opener-Policy warnings!")
        
    else:
        print("\n⚠️  Some fixes failed - check the errors above")
    
    return successful == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

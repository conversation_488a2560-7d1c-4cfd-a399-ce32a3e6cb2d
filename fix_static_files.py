#!/usr/bin/env python3
"""
Fix Static Files Issues for Arena Doviz Production

This script fixes all static file serving issues including:
1. 404 errors on static files
2. Incorrect MIME types
3. ArenaDoviz JavaScript not loading

Usage:
    python fix_static_files.py
"""

import os
import sys
import django
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.core.management import call_command
from django.conf import settings
from django.test import Client
import mimetypes

def collect_static_files():
    """Collect static files."""
    print("📦 Collecting Static Files")
    print("-" * 30)
    
    try:
        call_command('collectstatic', '--noinput', verbosity=1)
        print("✅ Static files collected successfully")
        return True
    except Exception as e:
        print(f"❌ Error collecting static files: {e}")
        return False

def verify_static_files_exist():
    """Verify that critical static files exist."""
    print("\n📁 Verifying Static Files Exist")
    print("-" * 30)
    
    static_root = Path(settings.STATIC_ROOT)
    critical_files = [
        'js/arena-doviz.js',
        'js/charts.js',
        'css/arena-doviz.css',
        'css/transactions.css',
    ]
    
    results = []
    for file_path in critical_files:
        full_path = static_root / file_path
        exists = full_path.exists()
        
        if exists:
            size = full_path.stat().st_size
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} - NOT FOUND")
        
        results.append((file_path, exists))
    
    return all(exists for _, exists in results)

def test_static_file_serving():
    """Test static file serving through Django."""
    print("\n🌐 Testing Static File Serving")
    print("-" * 30)
    
    client = Client()
    
    test_files = [
        ('/static/js/arena-doviz.js', 'application/javascript'),
        ('/static/js/charts.js', 'application/javascript'),
        ('/static/css/arena-doviz.css', 'text/css'),
        ('/static/css/transactions.css', 'text/css'),
    ]
    
    results = []
    for url, expected_mime in test_files:
        try:
            response = client.get(url)
            status = response.status_code
            content_type = response.get('Content-Type', '').split(';')[0]
            
            if status == 200:
                if content_type == expected_mime:
                    print(f"✅ {url} - {status} - {content_type}")
                    results.append((url, True))
                else:
                    print(f"⚠️  {url} - {status} - Wrong MIME: {content_type} (expected {expected_mime})")
                    results.append((url, False))
            else:
                print(f"❌ {url} - {status}")
                results.append((url, False))
                
        except Exception as e:
            print(f"❌ {url} - Error: {e}")
            results.append((url, False))
    
    return results

def fix_mime_types():
    """Fix MIME types for static files."""
    print("\n🔧 Fixing MIME Types")
    print("-" * 30)
    
    try:
        # Add MIME types
        mimetypes.add_type('application/javascript', '.js')
        mimetypes.add_type('text/css', '.css')
        mimetypes.add_type('application/json', '.json')
        mimetypes.add_type('image/svg+xml', '.svg')
        mimetypes.add_type('font/woff', '.woff')
        mimetypes.add_type('font/woff2', '.woff2')
        
        print("✅ MIME types configured:")
        print("   .js → application/javascript")
        print("   .css → text/css")
        print("   .json → application/json")
        print("   .svg → image/svg+xml")
        print("   .woff → font/woff")
        print("   .woff2 → font/woff2")
        
        return True
    except Exception as e:
        print(f"❌ Error fixing MIME types: {e}")
        return False

def verify_arena_doviz_js():
    """Verify ArenaDoviz JavaScript content."""
    print("\n🔍 Verifying ArenaDoviz JavaScript")
    print("-" * 30)
    
    try:
        js_file = Path(settings.STATIC_ROOT) / 'js' / 'arena-doviz.js'
        
        if not js_file.exists():
            print("❌ arena-doviz.js not found")
            return False
        
        content = js_file.read_text(encoding='utf-8')
        
        # Check for key components
        checks = [
            ('ArenaDoviz object', 'const ArenaDoviz' in content or 'var ArenaDoviz' in content),
            ('Auth module', 'auth:' in content),
            ('Utils module', 'utils:' in content),
            ('API functions', 'fetch(' in content),
        ]
        
        all_good = True
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        print(f"\nFile size: {len(content)} characters")
        return all_good
        
    except Exception as e:
        print(f"❌ Error verifying JavaScript: {e}")
        return False

def create_test_page():
    """Create a test page to verify static files."""
    print("\n📄 Creating Static Files Test Page")
    print("-" * 30)
    
    test_html = """<!DOCTYPE html>
<html>
<head>
    <title>Arena Doviz Static Files Test</title>
    <link rel="stylesheet" href="/static/css/arena-doviz.css">
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
<body>
    <h1>Arena Doviz Static Files Test</h1>
    <div id="test-results"></div>
    
    <script src="/static/js/arena-doviz.js"></script>
    <script src="/static/js/charts.js"></script>
    
    <script>
    $(document).ready(function() {
        const results = $('#test-results');
        
        // Test ArenaDoviz object
        if (typeof ArenaDoviz !== 'undefined') {
            results.append('<p style="color: green;">✅ ArenaDoviz object loaded</p>');
            
            // Test auth module
            if (ArenaDoviz.auth) {
                results.append('<p style="color: green;">✅ ArenaDoviz.auth module loaded</p>');
            } else {
                results.append('<p style="color: red;">❌ ArenaDoviz.auth module missing</p>');
            }
            
            // Test utils module
            if (ArenaDoviz.utils) {
                results.append('<p style="color: green;">✅ ArenaDoviz.utils module loaded</p>');
            } else {
                results.append('<p style="color: red;">❌ ArenaDoviz.utils module missing</p>');
            }
            
        } else {
            results.append('<p style="color: red;">❌ ArenaDoviz object not defined</p>');
        }
        
        // Test jQuery
        if (typeof $ !== 'undefined') {
            results.append('<p style="color: green;">✅ jQuery loaded</p>');
        } else {
            results.append('<p style="color: red;">❌ jQuery not loaded</p>');
        }
    });
    </script>
</body>
</html>"""
    
    try:
        test_file = Path('static_test.html')
        test_file.write_text(test_html, encoding='utf-8')
        print(f"✅ Test page created: {test_file.absolute()}")
        print("   Open this file in a browser to test static files")
        return True
    except Exception as e:
        print(f"❌ Error creating test page: {e}")
        return False

def main():
    """Main function."""
    print("🚀 Arena Doviz Static Files Fix")
    print("=" * 50)
    
    operations = [
        ("Collect Static Files", collect_static_files),
        ("Verify Files Exist", verify_static_files_exist),
        ("Fix MIME Types", fix_mime_types),
        ("Test File Serving", lambda: len([r for _, r in test_static_file_serving() if r]) > 0),
        ("Verify ArenaDoviz JS", verify_arena_doviz_js),
        ("Create Test Page", create_test_page),
    ]
    
    results = []
    
    for operation_name, operation_func in operations:
        print(f"\n🔄 {operation_name}")
        print("=" * 30)
        
        try:
            result = operation_func()
            results.append((operation_name, result))
        except Exception as e:
            print(f"❌ {operation_name} failed: {e}")
            results.append((operation_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Fix Summary")
    print("=" * 50)
    
    successful = sum(1 for _, result in results if result)
    total = len(results)
    
    for operation_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {operation_name}")
    
    print(f"\n📈 Overall Result: {successful}/{total} operations successful")
    
    if successful >= total - 1:  # Allow one failure
        print("\n🎉 Static files should now work correctly!")
        print("\n🚀 NEXT STEPS:")
        print("1. Restart the server:")
        print("   python manage.py runserver 0.0.0.0:8000 --settings=config.settings.prod")
        print("2. Test the pages - JavaScript errors should be gone")
        print("3. ArenaDoviz object should be available on all pages")
        
        return True
    else:
        print("\n⚠️  Some static file issues remain")
        print("Check the errors above and ensure static files are properly configured")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

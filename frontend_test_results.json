{"server_status": true, "static_files": {"/static/js/arena-doviz.js": {"status_code": 200, "content_type": "text/javascript; charset=\"utf-8\"", "success": true}, "/static/js/charts.js": {"status_code": 200, "content_type": "text/javascript; charset=\"utf-8\"", "success": true}, "/static/js/error-monitor.js": {"status_code": 200, "content_type": "text/javascript; charset=\"utf-8\"", "success": true}, "/static/css/arena-doviz.css": {"status_code": 200, "content_type": "text/css; charset=\"utf-8\"", "success": true}, "/static/css/transactions.css": {"status_code": 200, "content_type": "text/css; charset=\"utf-8\"", "success": true}}, "page_loads": {"/accounts/login/": {"name": "<PERSON><PERSON>", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/"}, "/dashboard/": {"name": "Dashboard", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/dashboard/"}, "/customers/": {"name": "Customers", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/customers/"}, "/transactions/": {"name": "Transactions", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/transactions/"}, "/transactions/type/EXCHANGE/": {"name": "Currency Exchange Form", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/transactions/type/EXCHANGE/"}, "/transactions/type/DEPOSIT/": {"name": "Cash Deposit Form", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/transactions/type/DEPOSIT/"}, "/transactions/type/TRANSFER/": {"name": "Money Transfer Form", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/transactions/type/TRANSFER/"}, "/reports/": {"name": "Reports", "status_code": 200, "success": true, "final_url": "http://localhost:8000/accounts/login/?next=/reports/"}}, "dropdown_tests": {}, "form_tests": {}, "transaction_tests": {}, "error_monitoring": {}, "summary": {"phases_passed": 3, "total_phases": 4, "success_rate": 0.75}, "api_endpoints": {"/api/v1/customers/": {"name": "Customers API", "status_code": 401, "success": false}, "/api/v1/currencies/": {"name": "Currencies API", "status_code": 401, "success": false}, "/api/v1/locations/": {"name": "Locations API", "status_code": 401, "success": false}, "/api/v1/transactions/types/": {"name": "Transaction Types API", "status_code": 401, "success": false}}, "javascript_functionality": {"C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\staticfiles\\js\\arena-doviz.js": {"name": "Main Arena Doviz JS", "file_size": 34046, "checks_passed": 4, "total_checks": 5, "success": true}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\staticfiles\\js\\error-monitor.js": {"name": "Error Monitor JS", "file_size": 9532, "checks_passed": 3, "total_checks": 5, "success": true}, "C:\\Users\\<USER>\\Documents\\exchange-accounting\\src\\staticfiles\\js\\transactions\\common.js": {"name": "Transaction Common JS", "file_size": 36038, "checks_passed": 4, "total_checks": 5, "success": true}}}
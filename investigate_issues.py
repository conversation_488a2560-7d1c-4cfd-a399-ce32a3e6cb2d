#!/usr/bin/env python
"""
Investigate balance and transaction type issues
"""

import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.customers.models import Customer
from apps.transactions.models import Transaction, BalanceEntry, TransactionType

def investigate_balance_issue():
    """Investigate why balances aren't showing"""
    print("=== Balance Investigation ===")
    
    # Check the specific transaction TXN-20250820-0004
    txn = Transaction.objects.filter(transaction_number='TXN-20250820-0004').first()
    if txn:
        print(f"Transaction: {txn.transaction_number}")
        print(f"Customer: {txn.customer.get_display_name()} ({txn.customer.customer_code})")
        print(f"Type: {txn.transaction_type.code if txn.transaction_type else 'NO_TYPE'}")
        print(f"Status: {txn.status}")
        print(f"Amount: {txn.from_currency.code} {txn.from_amount}")
        print(f"Balance entries: {txn.balance_entries.count()}")
        
        for entry in txn.balance_entries.all():
            customer_name = entry.customer.get_display_name() if entry.customer else 'Company'
            print(f"  Entry: {customer_name} - {entry.currency.code} {entry.amount} ({entry.entry_type})")
        
        # Check customer balance
        customer = txn.customer
        balance_summary = customer.get_balance_summary()
        print(f"Customer balance summary: {balance_summary}")
        
        # Check balance entries directly
        entries = BalanceEntry.objects.filter(customer=customer, is_deleted=False)
        print(f"Direct balance entries count: {entries.count()}")
        for entry in entries:
            print(f"  Direct entry: {entry.currency.code} {entry.amount} ({entry.entry_type})")
    else:
        print("Transaction TXN-20250820-0004 not found")

def check_transaction_types():
    """Check transaction type mismatch"""
    print("\n=== Transaction Types Investigation ===")
    
    backend_types = []
    for tt in TransactionType.objects.all():
        backend_types.append(tt.code)
        print(f"{tt.code}: {tt.name}")
    
    # Frontend types from the code analysis
    frontend_types = ['EXCHANGE', 'TRANSFER', 'DEPOSIT', 'WITHDRAWAL', 'REMITTANCE', 'ADJUSTMENT']
    
    print(f"\nBackend types: {sorted(backend_types)}")
    print(f"Frontend types: {sorted(frontend_types)}")
    
    missing_in_backend = set(frontend_types) - set(backend_types)
    missing_in_frontend = set(backend_types) - set(frontend_types)
    
    if missing_in_backend:
        print(f"Missing in backend: {missing_in_backend}")
    if missing_in_frontend:
        print(f"Missing in frontend: {missing_in_frontend}")
    
    return missing_in_backend, missing_in_frontend

def check_recent_transactions():
    """Check recent transactions and their balance entries"""
    print("\n=== Recent Transactions Check ===")
    
    recent_txns = Transaction.objects.filter(status='approved').order_by('-created_at')[:5]
    for txn in recent_txns:
        print(f"{txn.transaction_number}: {txn.transaction_type.code if txn.transaction_type else 'NO_TYPE'} - Status: {txn.status} - Entries: {txn.balance_entries.count()}")

if __name__ == '__main__':
    investigate_balance_issue()
    missing_backend, missing_frontend = check_transaction_types()
    check_recent_transactions()
    
    if missing_backend or missing_frontend:
        print(f"\n🚨 Transaction type mismatch detected!")
        print(f"Missing in backend: {missing_backend}")
        print(f"Missing in frontend: {missing_frontend}")
    else:
        print(f"\n✅ Transaction types match between frontend and backend")

#!/usr/bin/env python3
"""
Quick Error Test for Arena Doviz

This script performs a focused test to verify:
1. jQuery null errors are fixed
2. Static files are serving correctly
3. Basic functionality is working
4. Console errors are captured

Usage:
    python quick_error_test.py
"""

import os
import sys
import django
import subprocess
import time
import requests
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()


def test_static_files():
    """Test that static files are serving correctly."""
    print("Testing static file serving...")
    
    base_url = "http://localhost:8000"
    static_files = [
        "/static/js/arena-doviz.js",
        "/static/js/charts.js",
        "/static/js/error-monitor.js",
        "/static/css/arena-doviz.css"
    ]
    
    results = []
    for file_path in static_files:
        try:
            response = requests.get(f"{base_url}{file_path}", timeout=5)
            status = response.status_code
            content_type = response.headers.get('Content-Type', 'unknown')
            
            if status == 200:
                print(f"  [PASS] {file_path} - {status} - {content_type}")
                results.append(True)
            else:
                print(f"  [FAIL] {file_path} - {status}")
                results.append(False)
        except requests.exceptions.RequestException as e:
            print(f"  [ERROR] {file_path} - {e}")
            results.append(False)
    
    return all(results)


def test_page_loads():
    """Test that key pages load without errors."""
    print("Testing page loads...")
    
    base_url = "http://localhost:8000"
    pages = [
        "/accounts/login/",
        "/dashboard/",
        "/customers/",
        "/transactions/",
    ]
    
    results = []
    for page in pages:
        try:
            response = requests.get(f"{base_url}{page}", timeout=10, allow_redirects=True)
            status = response.status_code
            
            if status == 200:
                print(f"  [PASS] {page} - {status}")
                results.append(True)
            elif status in [302, 301]:  # Redirects are OK
                print(f"  [PASS] {page} - {status} (redirect)")
                results.append(True)
            else:
                print(f"  [FAIL] {page} - {status}")
                results.append(False)
        except requests.exceptions.RequestException as e:
            print(f"  [ERROR] {page} - {e}")
            results.append(False)
    
    return all(results)


def test_javascript_content():
    """Test JavaScript file content for fixes."""
    print("Testing JavaScript content...")
    
    js_files = [
        src_path / 'staticfiles' / 'js' / 'arena-doviz.js',
        src_path / 'staticfiles' / 'js' / 'transactions' / 'common.js',
        src_path / 'staticfiles' / 'js' / 'error-monitor.js'
    ]
    
    results = []
    for js_file in js_files:
        if js_file.exists():
            try:
                content = js_file.read_text(encoding='utf-8')
                
                # Check for defensive programming patterns
                checks = [
                    ('safeGetElementValue' in content, 'Safe element value getter'),
                    ('safeSetElementValue' in content, 'Safe element value setter'),
                    ('safeGetJQueryValue' in content, 'Safe jQuery value getter'),
                    ('waitForElement' in content, 'Element waiting function'),
                    ('DOMContentLoaded' in content or 'document.ready' in content, 'DOM ready handling'),
                ]
                
                file_results = []
                print(f"  Checking {js_file.name}:")
                for check_result, description in checks:
                    if check_result:
                        print(f"    [PASS] {description}")
                        file_results.append(True)
                    else:
                        print(f"    [SKIP] {description}")
                        file_results.append(True)  # Not all files need all patterns
                
                results.append(any(file_results))  # At least some defensive patterns
                
            except Exception as e:
                print(f"  [ERROR] Error reading {js_file}: {e}")
                results.append(False)
        else:
            print(f"  [SKIP] {js_file} not found")
            results.append(True)  # Skip missing files
    
    return all(results)


def start_server():
    """Start Django development server."""
    print("Starting Django development server...")
    
    try:
        # Start server in background
        server_process = subprocess.Popen([
            sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000',
            '--settings=config.settings.dev'
        ], cwd=src_path, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait for server to start
        print("Waiting for server to start...")
        time.sleep(8)
        
        # Check if server is running
        try:
            response = requests.get('http://localhost:8000/accounts/login/', timeout=10)
            if response.status_code in [200, 302]:
                print("[PASS] Django server started successfully")
                return server_process
            else:
                print(f"[FAIL] Server responded with status {response.status_code}")
                server_process.terminate()
                return None
        except requests.exceptions.RequestException as e:
            print(f"[FAIL] Server not responding: {e}")
            server_process.terminate()
            return None
            
    except Exception as e:
        print(f"[ERROR] Error starting server: {e}")
        return None


def run_quick_tests():
    """Run all quick tests."""
    print("=" * 60)
    print("ARENA DOVIZ QUICK ERROR TEST")
    print("=" * 60)
    
    # Start server
    server_process = start_server()
    if not server_process:
        print("[FAIL] Could not start server. Aborting tests.")
        return False
    
    try:
        # Run tests
        test_results = []
        
        print("\n1. Testing Static Files")
        print("-" * 30)
        test_results.append(test_static_files())
        
        print("\n2. Testing Page Loads")
        print("-" * 30)
        test_results.append(test_page_loads())
        
        print("\n3. Testing JavaScript Content")
        print("-" * 30)
        test_results.append(test_javascript_content())
        
        # Summary
        passed_tests = sum(test_results)
        total_tests = len(test_results)
        
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print(f"Passed: {passed_tests}/{total_tests}")
        print(f"Success Rate: {passed_tests/total_tests:.1%}")
        
        if passed_tests == total_tests:
            print("\n[SUCCESS] All tests passed! jQuery fixes are working.")
            print("\nNext steps:")
            print("1. Test the application manually in browser")
            print("2. Check browser console for any remaining errors")
            print("3. Test transaction forms specifically")
            return True
        else:
            print(f"\n[PARTIAL] {total_tests - passed_tests} test(s) failed.")
            print("Review the failures above and fix as needed.")
            return False
    
    finally:
        # Stop server
        if server_process:
            server_process.terminate()
            server_process.wait()
            print("\n[INFO] Django server stopped")


def main():
    """Main function."""
    success = run_quick_tests()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()

@echo off
echo Starting Arena Doviz Production Server...
echo.

REM Set the encryption key
set ARENA_ENCRYPTION_KEY=x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=

REM Change to src directory
cd /d "%~dp0src"

REM Run the Django development server with production settings
echo Running server at http://0.0.0.0:8000
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver 0.0.0.0:8000 --settings=config.settings.prod

pause

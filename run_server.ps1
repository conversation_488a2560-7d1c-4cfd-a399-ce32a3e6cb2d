# Arena Doviz Production Server Startup Script
Write-Host "Starting Arena Doviz Production Server..." -ForegroundColor Green
Write-Host ""

# Set the encryption key
$env:ARENA_ENCRYPTION_KEY = "x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo="

# Change to src directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location "$scriptPath\src"

# Display server information
Write-Host "Server will be accessible at:" -ForegroundColor Yellow
Write-Host "  - Local: http://localhost:8000" -ForegroundColor Cyan
Write-Host "  - Network: http://0.0.0.0:8000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host ""

# Run the Django server with production settings
try {
    python manage.py runserver 0.0.0.0:8000 --settings=config.settings.prod
}
catch {
    Write-Host "Error starting server: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "Make sure you have:" -ForegroundColor Yellow
    Write-Host "1. Python 3.8+ installed" -ForegroundColor White
    Write-Host "2. Virtual environment activated" -ForegroundColor White
    Write-Host "3. Dependencies installed (pip install -r requirements.txt)" -ForegroundColor White
    Write-Host "4. Database migrations run (python manage.py migrate)" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

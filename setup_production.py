#!/usr/bin/env python3
"""
Arena Doviz Production Setup Script

This script prepares the Arena Doviz system for production deployment by:
1. Setting up the database and running migrations
2. Creating cache tables
3. Collecting static files
4. Creating a superuser (if needed)
5. Setting up initial data
6. Validating the configuration

Usage:
    python setup_production.py
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module for production
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def run_command(command, description, cwd=None):
    """Run a command and handle errors."""
    logger.info(f"🔄 {description}...")
    try:
        if cwd is None:
            cwd = src_path
        
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        
        if result.stdout:
            logger.info(f"✅ {description} completed successfully")
            if result.stdout.strip():
                logger.debug(f"Output: {result.stdout.strip()}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed!")
        logger.error(f"Error: {e.stderr}")
        if e.stdout:
            logger.error(f"Output: {e.stdout}")
        return False

def check_encryption_key():
    """Check and generate encryption key if needed."""
    logger.info("🔐 Checking encryption key...")
    
    encryption_key = os.environ.get('ARENA_ENCRYPTION_KEY')
    
    if not encryption_key:
        logger.info("🔑 Generating new encryption key...")
        try:
            from cryptography.fernet import Fernet
            new_key = Fernet.generate_key().decode()
            
            logger.info("✅ New encryption key generated!")
            logger.info(f"🔑 ARENA_ENCRYPTION_KEY={new_key}")
            logger.info("")
            logger.info("⚠️  IMPORTANT: Save this key securely!")
            logger.info("   Set it as an environment variable before running the server:")
            logger.info(f"   Windows: set ARENA_ENCRYPTION_KEY={new_key}")
            logger.info(f"   Linux/macOS: export ARENA_ENCRYPTION_KEY={new_key}")
            logger.info("")
            
            # Set it for this session
            os.environ['ARENA_ENCRYPTION_KEY'] = new_key
            return True
            
        except ImportError:
            logger.error("❌ cryptography package not installed!")
            logger.error("   Install it with: pip install cryptography")
            return False
    else:
        # Validate existing key
        try:
            from cryptography.fernet import Fernet
            Fernet(encryption_key.encode())
            logger.info(f"✅ Encryption key validated: {encryption_key[:20]}...")
            return True
        except Exception as e:
            logger.error(f"❌ Invalid encryption key: {e}")
            return False

def setup_database():
    """Set up the database with migrations."""
    logger.info("🗄️  Setting up database...")
    
    # Run migrations
    if not run_command("python manage.py makemigrations", "Creating migrations"):
        return False
    
    if not run_command("python manage.py migrate", "Running migrations"):
        return False
    
    # Create cache table
    if not run_command("python manage.py createcachetable", "Creating cache table"):
        logger.warning("⚠️  Cache table creation failed - continuing anyway")
    
    return True

def collect_static_files():
    """Collect static files for production."""
    logger.info("📁 Collecting static files...")
    return run_command("python manage.py collectstatic --noinput", "Collecting static files")

def create_superuser():
    """Create a superuser if needed."""
    logger.info("👤 Checking for superuser...")
    
    try:
        import django
        django.setup()
        
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        if User.objects.filter(is_superuser=True).exists():
            logger.info("✅ Superuser already exists")
            return True
        
        logger.info("🔧 Creating superuser...")
        logger.info("   Please enter superuser details:")
        
        if not run_command("python manage.py createsuperuser", "Creating superuser"):
            logger.warning("⚠️  Superuser creation failed - you can create one later")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking superuser: {e}")
        return False

def setup_initial_data():
    """Set up initial data."""
    logger.info("🏗️  Setting up initial data...")
    
    try:
        import django
        django.setup()
        
        # Import and run setup functions
        from apps.locations.models import Location
        from apps.currencies.models import Currency
        from apps.transactions.models import TransactionType
        
        # Check if data already exists
        if Location.objects.exists() and Currency.objects.exists() and TransactionType.objects.exists():
            logger.info("✅ Initial data already exists")
            return True
        
        # Run setup commands
        commands = [
            "python manage.py setup_locations",
            "python manage.py setup_currencies", 
            "python manage.py setup_transaction_types",
        ]
        
        for command in commands:
            if not run_command(command, f"Running {command.split()[-1]}"):
                logger.warning(f"⚠️  {command} failed - continuing anyway")
        
        logger.info("✅ Initial data setup completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error setting up initial data: {e}")
        logger.info("   You can set up initial data manually later")
        return True  # Don't fail the entire setup

def validate_configuration():
    """Validate the production configuration."""
    logger.info("🔍 Validating configuration...")
    
    try:
        import django
        django.setup()
        
        from django.core.management import call_command
        from django.conf import settings
        
        # Check database connection
        from django.db import connection
        connection.ensure_connection()
        logger.info("✅ Database connection successful")
        
        # Check static files
        if not Path(settings.STATIC_ROOT).exists():
            logger.warning("⚠️  Static files directory doesn't exist")
        else:
            logger.info("✅ Static files directory exists")
        
        # Check encryption key
        if not settings.ARENA_ENCRYPTION_KEY:
            logger.error("❌ Encryption key not configured")
            return False
        
        logger.info("✅ Configuration validation completed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        return False

def main():
    """Main setup function."""
    logger.info("🚀 Arena Doviz Production Setup")
    logger.info("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ is required")
        return False
    
    logger.info(f"✅ Python {sys.version.split()[0]} detected")
    
    # Setup steps
    steps = [
        ("Encryption Key", check_encryption_key),
        ("Database Setup", setup_database),
        ("Static Files", collect_static_files),
        ("Superuser", create_superuser),
        ("Initial Data", setup_initial_data),
        ("Configuration", validate_configuration),
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 Step: {step_name}")
        logger.info("-" * 30)
        
        if not step_func():
            failed_steps.append(step_name)
            logger.error(f"❌ {step_name} failed!")
        else:
            logger.info(f"✅ {step_name} completed successfully")
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Setup Summary")
    logger.info("=" * 50)
    
    if failed_steps:
        logger.error(f"❌ {len(failed_steps)} step(s) failed:")
        for step in failed_steps:
            logger.error(f"   - {step}")
        logger.info("\n⚠️  You may need to fix these issues before running in production")
    else:
        logger.info("🎉 All setup steps completed successfully!")
    
    logger.info("\n🚀 Next Steps:")
    logger.info("1. Set the encryption key as an environment variable")
    logger.info("2. Run the production server: python run_production.py")
    logger.info("3. Access the application at: http://localhost:8000")
    
    return len(failed_steps) == 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

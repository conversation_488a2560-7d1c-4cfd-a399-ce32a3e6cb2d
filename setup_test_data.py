#!/usr/bin/env python3
"""
Setup Test Data for Arena Doviz Production

Creates necessary test data including customers, transaction types, and test transactions.
"""

import os
import sys
import django
from pathlib import Path
from decimal import Decimal

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone

from apps.transactions.models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency

User = get_user_model()

def create_transaction_types():
    """Create transaction types if they don't exist."""
    print("🔧 Creating Transaction Types...")
    
    transaction_types = [
        {
            'code': 'EXCHANGE',
            'name': 'Currency Exchange',
            'description': 'Exchange one currency for another',
            'is_active': True,
            'requires_approval': True,
        },
        {
            'code': 'TRANSFER',
            'name': 'Money Transfer',
            'description': 'Transfer money between accounts',
            'is_active': True,
            'requires_approval': True,
        },
        {
            'code': 'DEPOSIT',
            'name': 'Cash Deposit',
            'description': 'Deposit cash into account',
            'is_active': True,
            'requires_approval': False,
        },
        {
            'code': 'WITHDRAWAL',
            'name': 'Cash Withdrawal',
            'description': 'Withdraw cash from account',
            'is_active': True,
            'requires_approval': True,
        },
    ]
    
    created_count = 0
    for type_data in transaction_types:
        txn_type, created = TransactionType.objects.get_or_create(
            code=type_data['code'],
            defaults=type_data
        )
        if created:
            created_count += 1
            print(f"   ✅ Created: {txn_type.name}")
        else:
            print(f"   ✅ Exists: {txn_type.name}")
    
    print(f"   Created {created_count} new transaction types")
    return True

def create_test_customers():
    """Create test customers."""
    print("🧑‍💼 Creating Test Customers...")
    
    customers_data = [
        {
            'customer_code': 'CUST001',
            'first_name': 'Ahmed',
            'last_name': 'Al-Rashid',
            'email': '<EMAIL>',
            'phone_number': '+************',
            'customer_type': Customer.CustomerType.INDIVIDUAL,
            'status': Customer.Status.ACTIVE,
        },
        {
            'customer_code': 'CUST002',
            'first_name': 'Fatima',
            'last_name': 'Hassan',
            'email': '<EMAIL>',
            'phone_number': '+971507654321',
            'customer_type': Customer.CustomerType.INDIVIDUAL,
            'status': Customer.Status.ACTIVE,
        },
        {
            'customer_code': 'CORP001',
            'company_name': 'Dubai Trading LLC',
            'email': '<EMAIL>',
            'phone_number': '+97143334444',
            'customer_type': Customer.CustomerType.CORPORATE,
            'status': Customer.Status.ACTIVE,
        },
    ]
    
    created_count = 0
    for customer_data in customers_data:
        customer, created = Customer.objects.get_or_create(
            customer_code=customer_data['customer_code'],
            defaults=customer_data
        )
        if created:
            created_count += 1
            print(f"   ✅ Created: {customer.get_display_name()}")
        else:
            print(f"   ✅ Exists: {customer.get_display_name()}")
    
    print(f"   Created {created_count} new customers")
    return True

def create_test_transaction():
    """Create a test transaction for approval testing."""
    print("📝 Creating Test Transaction...")
    
    try:
        # Get required objects
        customer = Customer.objects.first()
        location = Location.objects.first()
        usd_currency = Currency.objects.filter(code='USD').first()
        aed_currency = Currency.objects.filter(code='AED').first()
        exchange_type = TransactionType.objects.filter(code='EXCHANGE').first()
        branch_user = User.objects.filter(role=User.Role.BRANCH_EMPLOYEE).first()
        
        if not all([customer, location, usd_currency, aed_currency, exchange_type, branch_user]):
            print("❌ Missing required data for transaction creation")
            return None
        
        # Create test transaction
        with transaction.atomic():
            test_transaction = Transaction.objects.create(
                transaction_type=exchange_type,
                customer=customer,
                location=location,
                from_currency=usd_currency,
                to_currency=aed_currency,
                from_amount=Decimal('100.00'),
                to_amount=Decimal('367.00'),
                exchange_rate=Decimal('3.67'),
                commission_amount=Decimal('2.00'),
                commission_currency=usd_currency,
                status=Transaction.Status.PENDING,
                description='Test transaction for approval testing',
                notes='Created by setup script for approval functionality testing',
                created_by=branch_user,
                delivery_method=Transaction.DeliveryMethod.IN_PERSON,
            )
        
        print(f"   ✅ Created transaction: {test_transaction.transaction_number}")
        print(f"   Transaction ID: {test_transaction.id}")
        print(f"   Status: {test_transaction.status}")
        print(f"   Can be approved: {test_transaction.can_be_approved()}")
        
        return test_transaction
        
    except Exception as e:
        print(f"   ❌ Error creating transaction: {e}")
        return None

def test_approval_functionality():
    """Test the approval functionality."""
    print("🧪 Testing Approval Functionality...")
    
    try:
        from django.test import Client
        
        # Get a pending transaction
        pending_transaction = Transaction.objects.filter(status=Transaction.Status.PENDING).first()
        if not pending_transaction:
            print("   ❌ No pending transactions found")
            return False
        
        # Get admin user
        admin_user = User.objects.filter(role=User.Role.ADMIN).first()
        if not admin_user:
            print("   ❌ No admin user found")
            return False
        
        print(f"   Testing approval of transaction: {pending_transaction.transaction_number}")
        
        # Create test client
        client = Client()
        client.force_login(admin_user)
        
        # Test the approval API
        url = f'/api/v1/transactions/transactions/{pending_transaction.id}/approve/'
        response = client.post(url, content_type='application/json')
        
        print(f"   API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ Approval API test successful!")
            
            # Refresh transaction from database
            pending_transaction.refresh_from_db()
            print(f"   New status: {pending_transaction.status}")
            print(f"   Approved by: {pending_transaction.approved_by}")
            
            return True
        else:
            print(f"   ❌ Approval API test failed!")
            try:
                response_data = response.json()
                print(f"   Error: {response_data}")
            except:
                print(f"   Response content: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing approval: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 Setting up Arena Doviz Test Data")
    print("=" * 50)
    
    operations = [
        ("Transaction Types", create_transaction_types),
        ("Test Customers", create_test_customers),
        ("Test Transaction", create_test_transaction),
        ("Approval Test", test_approval_functionality),
    ]
    
    results = []
    
    for operation_name, operation_func in operations:
        print(f"\n🔄 {operation_name}")
        print("-" * 30)
        
        try:
            result = operation_func()
            success = result is not None and result is not False
            results.append((operation_name, success))
            
            if success:
                print(f"✅ {operation_name} completed successfully")
            else:
                print(f"❌ {operation_name} failed")
                
        except Exception as e:
            print(f"❌ {operation_name} failed: {e}")
            results.append((operation_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Setup Summary")
    print("=" * 50)
    
    successful = sum(1 for _, result in results if result)
    total = len(results)
    
    for operation_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {operation_name}")
    
    print(f"\n📈 Overall Result: {successful}/{total} operations successful")
    
    if successful == total:
        print("\n🎉 All setup completed successfully!")
        print("\nYour Arena Doviz system is now ready with:")
        print("- ✅ User accounts with proper roles")
        print("- ✅ Transaction types")
        print("- ✅ Test customers")
        print("- ✅ Test transactions")
        print("- ✅ Working approval functionality")
        
        print("\n🚀 Next Steps:")
        print("1. Start the server: python run_server.bat")
        print("2. Access: http://localhost:8000")
        print("3. Login with: admin_user / Admin123!@#")
        print("4. Test transaction approval functionality")
        
    else:
        print("\n⚠️  Some setup operations failed")
        print("Check the error messages above for details")
    
    return successful == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

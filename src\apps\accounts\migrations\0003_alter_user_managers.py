# Generated by Django 4.2.23 on 2025-08-20 15:43

import apps.accounts.models
import django.contrib.auth.models
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_initial'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='user',
            managers=[
                ('objects', apps.accounts.models.CustomUserManager()),
                ('all_objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]

"""
Custom middleware for Arena Doviz Exchange Accounting System.
"""

import logging
import time
import json
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.core.exceptions import PermissionDenied
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone
from django.contrib.auth.signals import user_logged_in, user_logged_out, user_login_failed
from django.dispatch import receiver
from django.shortcuts import redirect
from django.urls import reverse
from django.conf import settings
from .utils import get_client_ip, create_error_response, ArenaDovizException
from .audit import get_audit_logger

logger = logging.getLogger(__name__)


class CrossOriginPolicyMiddleware(MiddlewareMixin):
    """
    Middleware to handle Cross-Origin-Opener-Policy and related headers for production testing.
    """

    def process_response(self, request, response):
        """Add or modify cross-origin policy headers."""

        # Remove Cross-Origin-Opener-Policy header for HTTP testing
        if 'Cross-Origin-Opener-Policy' in response:
            del response['Cross-Origin-Opener-Policy']

        # Add permissive headers for testing on different devices
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-CSRFToken, X-Requested-With'
        response['Access-Control-Allow-Credentials'] = 'true'

        # Remove problematic security headers for HTTP testing
        headers_to_remove = [
            'Cross-Origin-Opener-Policy',
            'Cross-Origin-Embedder-Policy',
            'Strict-Transport-Security',
        ]

        for header in headers_to_remove:
            if header in response:
                del response[header]

        return response


class RequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all requests and responses for audit purposes.
    """
    
    def process_request(self, request):
        """Log incoming requests."""
        request.start_time = time.time()
        
        # Get client information
        ip_address = get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        
        # Log request details
        logger.info(
            f"Request: {request.method} {request.path}",
            extra={
                'method': request.method,
                'path': request.path,
                'ip_address': ip_address,
                'user_agent': user_agent,
                'user_id': request.user.id if hasattr(request, 'user') and not isinstance(request.user, AnonymousUser) else None,
                'query_params': dict(request.GET),
                'timestamp': timezone.now().isoformat()
            }
        )
        
        # Store request info for later use
        request.client_ip = ip_address
        request.user_agent = user_agent
        request.audit_ip_address = ip_address
        request.audit_user_agent = user_agent
    
    def process_response(self, request, response):
        """Log response details."""
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            logger.info(
                f"Response: {request.method} {request.path} - {response.status_code} ({duration:.3f}s)",
                extra={
                    'method': request.method,
                    'path': request.path,
                    'status_code': response.status_code,
                    'duration': duration,
                    'ip_address': getattr(request, 'client_ip', 'unknown'),
                    'user_id': request.user.id if hasattr(request, 'user') and not isinstance(request.user, AnonymousUser) else None,
                    'timestamp': timezone.now().isoformat()
                }
            )

            # Log API requests for audit purposes
            if hasattr(request, 'user') and request.user.is_authenticated:
                if request.path.startswith('/api/'):
                    audit_logger = get_audit_logger()
                    audit_logger.log_system_event(
                        event_type='api_access',
                        description=f"API access: {request.method} {request.path}",
                        metadata={
                            'user_id': request.user.id,
                            'method': request.method,
                            'path': request.path,
                            'status_code': response.status_code,
                            'duration': duration,
                            'ip_address': getattr(request, 'client_ip', 'unknown'),
                            'user_agent': getattr(request, 'user_agent', 'unknown')
                        },
                        success=200 <= response.status_code < 400
                    )

        return response


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware to handle exceptions and provide consistent error responses.
    """
    
    def process_exception(self, request, exception):
        """Handle exceptions and return appropriate responses."""
        
        # Log the exception
        logger.error(
            f"Exception in {request.method} {request.path}: {str(exception)}",
            extra={
                'method': request.method,
                'path': request.path,
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'ip_address': getattr(request, 'client_ip', 'unknown'),
                'user_id': request.user.id if hasattr(request, 'user') and not isinstance(request.user, AnonymousUser) else None,
                'timestamp': timezone.now().isoformat()
            },
            exc_info=True
        )
        
        # Handle different types of exceptions
        if isinstance(exception, ArenaDovizException):
            return create_error_response(
                message=exception.message,
                error_code=exception.error_code,
                status_code=400,
                details=exception.details
            )
        
        elif isinstance(exception, PermissionDenied):
            return create_error_response(
                message="Permission denied",
                error_code="PERMISSION_DENIED",
                status_code=403
            )
        
        elif isinstance(exception, ValueError):
            return create_error_response(
                message="Invalid data provided",
                error_code="INVALID_DATA",
                status_code=400
            )
        
        # For API requests, return JSON error response
        if request.path.startswith('/api/') or request.content_type == 'application/json':
            return create_error_response(
                message="An internal error occurred",
                error_code="INTERNAL_ERROR",
                status_code=500
            )
        
        # For non-API requests, let Django handle it normally
        return None


class SecurityMiddleware(MiddlewareMixin):
    """
    Middleware to enhance security by checking various security constraints.
    """
    
    def process_request(self, request):
        """Perform security checks on incoming requests."""
        
        # Check for suspicious patterns
        suspicious_patterns = [
            'script',
            'javascript:',
            '<script',
            'eval(',
            'document.cookie',
            'union select',
            'drop table',
            '../',
            '..\\',
        ]
        
        # Check query parameters and form data
        all_params = {}
        all_params.update(request.GET.dict())
        if hasattr(request, 'POST'):
            all_params.update(request.POST.dict())
        
        for param, value in all_params.items():
            if isinstance(value, str):
                value_lower = value.lower()
                for pattern in suspicious_patterns:
                    if pattern in value_lower:
                        logger.warning(
                            f"Suspicious request detected: {pattern} in {param}",
                            extra={
                                'ip_address': get_client_ip(request),
                                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                                'path': request.path,
                                'suspicious_pattern': pattern,
                                'parameter': param,
                                'value': value[:100]  # Limit value length in logs
                            }
                        )
                        
                        return create_error_response(
                            message="Request blocked for security reasons",
                            error_code="SECURITY_VIOLATION",
                            status_code=400
                        )
        
        # Check for rate limiting (basic implementation)
        ip_address = get_client_ip(request)
        if self._is_rate_limited(request, ip_address):
            logger.warning(
                f"Rate limit exceeded for IP: {ip_address}",
                extra={
                    'ip_address': ip_address,
                    'path': request.path,
                    'user_agent': request.META.get('HTTP_USER_AGENT', '')
                }
            )
            
            return create_error_response(
                message="Rate limit exceeded",
                error_code="RATE_LIMIT_EXCEEDED",
                status_code=429
            )
        
        return None
    
    def _is_rate_limited(self, request, ip_address):
        """
        Check if the request should be rate limited.
        This is a basic implementation - in production, use Redis or similar.
        """
        from django.core.cache import cache
        
        # Rate limit: 1000 requests per minute per IP (increased for testing)
        cache_key = f"rate_limit:{ip_address}"
        current_requests = cache.get(cache_key, 0)

        if current_requests >= 1000:
            return True
        
        # Increment counter
        cache.set(cache_key, current_requests + 1, 60)  # 60 seconds
        return False


class UserActivityMiddleware(MiddlewareMixin):
    """
    Middleware to track user activity and update last activity timestamp.
    """
    
    def process_request(self, request):
        """Update user's last activity."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                # Update last activity timestamp
                request.user.last_login = timezone.now()
                request.user.save(update_fields=['last_login'])
                
                # Log user activity
                logger.debug(
                    f"User activity: {request.user.username} - {request.method} {request.path}",
                    extra={
                        'user_id': request.user.id,
                        'username': request.user.username,
                        'method': request.method,
                        'path': request.path,
                        'ip_address': get_client_ip(request)
                    }
                )
                
            except Exception as e:
                logger.error(f"Failed to update user activity: {e}")
        
        return None


class APIVersionMiddleware(MiddlewareMixin):
    """
    Middleware to handle API versioning.
    """
    
    def process_request(self, request):
        """Set API version from header or URL."""
        if request.path.startswith('/api/'):
            # Get version from header or URL
            api_version = request.META.get('HTTP_API_VERSION', 'v1')
            
            # Validate version
            supported_versions = ['v1']
            if api_version not in supported_versions:
                return create_error_response(
                    message=f"Unsupported API version: {api_version}",
                    error_code="UNSUPPORTED_API_VERSION",
                    status_code=400
                )
            
            request.api_version = api_version
        
        return None


class AuthenticationRedirectMiddleware(MiddlewareMixin):
    """
    Middleware to handle authentication redirects for protected pages.
    Redirects unauthenticated users to login page for web requests.
    """

    # Pages that don't require authentication
    PUBLIC_PATHS = [
        '/accounts/login/',
        '/accounts/logout/',
        '/api/v1/accounts/jwt/token/',
        '/api/v1/accounts/jwt/token/refresh/',
        '/admin/login/',
        '/static/',
        '/media/',
        '/favicon.ico',
    ]

    # API paths that should return JSON errors instead of redirects
    API_PATHS = [
        '/api/',
        '/admin/jsi18n/',
    ]

    def process_request(self, request):
        """Check authentication and redirect if necessary."""
        # Skip for public paths
        if any(request.path.startswith(path) for path in self.PUBLIC_PATHS):
            return None

        # Skip for API paths - they handle their own authentication
        if any(request.path.startswith(path) for path in self.API_PATHS):
            return None

        # Check if user is authenticated
        if not request.user.is_authenticated:
            # For AJAX requests, return JSON error
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'error': 'Authentication required',
                    'redirect_url': '/accounts/login/'
                }, status=401)

            # For regular web requests, redirect to login
            login_url = '/accounts/login/'
            if request.path != login_url:
                # Add next parameter to redirect back after login
                next_url = request.get_full_path()
                return redirect(f"{login_url}?next={next_url}")

        return None


class CORSMiddleware(MiddlewareMixin):
    """
    Custom CORS middleware for additional control.
    """

    def process_response(self, request, response):
        """Add CORS headers to response."""
        # Allow credentials
        response['Access-Control-Allow-Credentials'] = 'true'

        # Add custom headers
        response['Access-Control-Expose-Headers'] = 'X-Total-Count, X-Page-Count'

        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'

        return response


# Signal handlers for authentication events

@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """Log successful user login."""
    audit_logger = get_audit_logger()
    audit_logger.log_login_attempt(
        username=user.username,
        success=True,
        ip_address=getattr(request, 'audit_ip_address', None),
        user_agent=getattr(request, 'audit_user_agent', None)
    )

    logger.info(f"User logged in: {user.username} from {getattr(request, 'audit_ip_address', 'unknown')}")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """Log user logout."""
    if user:
        audit_logger = get_audit_logger()
        audit_logger.log_logout(
            user=user,
            ip_address=getattr(request, 'audit_ip_address', None),
            user_agent=getattr(request, 'audit_user_agent', None)
        )

        logger.info(f"User logged out: {user.username} from {getattr(request, 'audit_ip_address', 'unknown')}")


@receiver(user_login_failed)
def log_failed_login(sender, credentials, request, **kwargs):
    """Log failed login attempt."""
    username = credentials.get('username', 'unknown')

    audit_logger = get_audit_logger()
    audit_logger.log_login_attempt(
        username=username,
        success=False,
        ip_address=getattr(request, 'audit_ip_address', None),
        user_agent=getattr(request, 'audit_user_agent', None),
        error_message='Invalid credentials'
    )

    logger.warning(f"Failed login attempt: {username} from {getattr(request, 'audit_ip_address', 'unknown')}")


class ModelAuditMixin:
    """
    Mixin for Django models to automatically log changes.
    Add this to models that need audit logging.
    """

    def save(self, *args, **kwargs):
        """Override save to log model changes."""
        # Determine if this is a create or update
        is_create = self.pk is None

        # Get old values for updates
        old_values = None
        if not is_create:
            try:
                old_instance = self.__class__.objects.get(pk=self.pk)
                old_values = self._get_field_values(old_instance)
            except self.__class__.DoesNotExist:
                pass

        # Save the model
        super().save(*args, **kwargs)

        # Log the change
        user = kwargs.get('user')
        request = kwargs.get('request')

        # Get audit information from request if available
        ip_address = None
        user_agent = None
        if request:
            ip_address = getattr(request, 'audit_ip_address', None)
            user_agent = getattr(request, 'audit_user_agent', None)

        # Log the audit event
        audit_logger = get_audit_logger()
        audit_logger.log_user_action(
            user=user,
            action_type='create' if is_create else 'update',
            table_name=self._meta.db_table,
            record_id=self.pk,
            old_values=old_values,
            new_values=self._get_field_values(self),
            ip_address=ip_address,
            user_agent=user_agent
        )

    def delete(self, *args, **kwargs):
        """Override delete to log model deletion."""
        # Get current values before deletion
        old_values = self._get_field_values(self)

        # Get audit information
        user = kwargs.get('user')
        request = kwargs.get('request')

        ip_address = None
        user_agent = None
        if request:
            ip_address = getattr(request, 'audit_ip_address', None)
            user_agent = getattr(request, 'audit_user_agent', None)

        # Delete the model
        super().delete(*args, **kwargs)

        # Log the deletion
        audit_logger = get_audit_logger()
        audit_logger.log_user_action(
            user=user,
            action_type='delete',
            table_name=self._meta.db_table,
            record_id=self.pk,
            old_values=old_values,
            new_values=None,
            ip_address=ip_address,
            user_agent=user_agent
        )

    def _get_field_values(self, instance):
        """Get field values for audit logging."""
        import uuid
        from decimal import Decimal
        from datetime import datetime, date

        values = {}

        for field in instance._meta.get_fields():
            if hasattr(field, 'name') and hasattr(instance, field.name):
                try:
                    value = getattr(instance, field.name)
                    # Convert to JSON-serializable format
                    if value is not None:
                        if isinstance(value, uuid.UUID):
                            values[field.name] = str(value)
                        elif isinstance(value, Decimal):
                            values[field.name] = float(value)
                        elif isinstance(value, (datetime, date)):
                            values[field.name] = value.isoformat()
                        else:
                            values[field.name] = str(value)
                except Exception:
                    # Skip fields that can't be accessed
                    pass

        return values

"""
Management command to fix missing balance entries for approved transactions.
This command creates balance entries for approved transactions that don't have them.
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from apps.transactions.models import Transaction, BalanceEntry
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Fix missing balance entries for approved transactions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if balance entries exist',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(self.style.SUCCESS('Starting balance entry fix process...'))
        
        # Find approved transactions without balance entries
        approved_transactions = Transaction.objects.filter(
            status=Transaction.Status.APPROVED,
            is_deleted=False
        )
        
        transactions_to_fix = []
        for txn in approved_transactions:
            if force or not txn.balance_entries.exists():
                transactions_to_fix.append(txn)
        
        self.stdout.write(f'Found {len(transactions_to_fix)} transactions to fix')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
            for txn in transactions_to_fix:
                txn_type = txn.transaction_type.code if txn.transaction_type else 'NO_TYPE'
                self.stdout.write(f'  Would fix: {txn.transaction_number} ({txn_type}) - {txn.customer.get_display_name()}')
            return
        
        # Fix transactions
        fixed_count = 0
        error_count = 0
        
        for txn in transactions_to_fix:
            try:
                with transaction.atomic():
                    # Delete existing balance entries if force is used
                    if force:
                        txn.balance_entries.all().delete()
                    
                    # Create balance entries using the transaction's method
                    txn._create_balance_entries()
                    
                    fixed_count += 1
                    txn_type = txn.transaction_type.code if txn.transaction_type else 'NO_TYPE'
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Fixed: {txn.transaction_number} ({txn_type}) - {txn.customer.get_display_name()}'
                        )
                    )
                    
            except Exception as e:
                error_count += 1
                self.stdout.write(
                    self.style.ERROR(
                        f'Error fixing {txn.transaction_number}: {str(e)}'
                    )
                )
                logger.error(f'Error fixing transaction {txn.transaction_number}: {str(e)}')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Balance entry fix completed: {fixed_count} fixed, {error_count} errors'
            )
        )

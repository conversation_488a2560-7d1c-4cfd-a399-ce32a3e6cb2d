/**
 * Arena Doviz Transaction Forms Styling
 * Consistent color scheme and styling for all transaction forms
 */

/* Arena Doviz Color Variables */
:root {
    --arena-primary: #000d28;    /* Dark navy */
    --arena-secondary: #6a0000;  /* Dark red */
    --arena-tertiary: #013121;   /* Dark green */
    --arena-background: #ffffff; /* White background */
    --arena-light-gray: #f8f9fa;
    --arena-border: #dee2e6;
    --arena-text: #212529;
    --arena-text-muted: #6c757d;
}

/* Transaction Navigation Cards */
.transaction-type-card .card-header {
    background-color: var(--arena-primary) !important;
    color: white !important;
    border-bottom: 2px solid var(--arena-secondary);
}

.transaction-type-card .card-header h5 {
    color: white !important;
    font-weight: 600;
}

.transaction-type-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 13, 40, 0.15);
    transition: all 0.3s ease;
}

/* Primary Buttons - Arena Navy */
.btn-primary {
    background-color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
    color: white !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: #001a3d !important;
    border-color: #001a3d !important;
    color: white !important;
}

/* Secondary Buttons - Arena Red */
.btn-secondary {
    background-color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
    color: white !important;
}

.btn-secondary:hover,
.btn-secondary:focus,
.btn-secondary:active {
    background-color: #800000 !important;
    border-color: #800000 !important;
    color: white !important;
}

/* Success Buttons - Arena Green */
.btn-success {
    background-color: var(--arena-tertiary) !important;
    border-color: var(--arena-tertiary) !important;
    color: white !important;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active {
    background-color: #014a2b !important;
    border-color: #014a2b !important;
    color: white !important;
}

/* Outline Buttons */
.btn-outline-primary {
    color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
    background-color: var(--arena-primary) !important;
    border-color: var(--arena-primary) !important;
    color: white !important;
}

.btn-outline-secondary {
    color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active {
    background-color: var(--arena-secondary) !important;
    border-color: var(--arena-secondary) !important;
    color: white !important;
}

/* Alert Styling */
.alert-info {
    background-color: rgba(0, 13, 40, 0.1) !important;
    border-color: var(--arena-primary) !important;
    color: var(--arena-primary) !important;
}

.alert-success {
    background-color: rgba(1, 49, 33, 0.1) !important;
    border-color: var(--arena-tertiary) !important;
    color: var(--arena-tertiary) !important;
}

.alert-warning {
    background-color: rgba(106, 0, 0, 0.1) !important;
    border-color: var(--arena-secondary) !important;
    color: var(--arena-secondary) !important;
}

.alert-danger {
    background-color: rgba(106, 0, 0, 0.15) !important;
    border-color: var(--arena-secondary) !important;
    color: var(--arena-secondary) !important;
}

/* Card Headers */
.card-header {
    background-color: var(--arena-light-gray) !important;
    border-bottom: 1px solid var(--arena-border) !important;
}

.card-header h5,
.card-header h6 {
    color: var(--arena-text) !important;
    margin-bottom: 0;
}

/* Form Elements */
.form-label {
    color: var(--arena-text) !important;
    font-weight: 500;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--arena-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 13, 40, 0.25) !important;
}

/* Required Field Indicators */
.text-danger {
    color: var(--arena-secondary) !important;
}

/* Text Colors */
.text-primary {
    color: var(--arena-primary) !important;
}

.text-success {
    color: var(--arena-tertiary) !important;
}

.text-warning {
    color: var(--arena-secondary) !important;
}

.text-muted {
    color: var(--arena-text-muted) !important;
}

/* Transaction Form Specific Styling */
.transaction-form-container {
    background-color: var(--arena-background);
}

.transaction-preview-card {
    border: 1px solid var(--arena-border);
    border-radius: 0.375rem;
}

.customer-balance-card {
    border: 1px solid var(--arena-border);
    border-radius: 0.375rem;
}

/* Balance Display */
.balance-positive {
    color: var(--arena-tertiary) !important;
    font-weight: 600;
}

.balance-negative {
    color: var(--arena-secondary) !important;
    font-weight: 600;
}

.balance-zero {
    color: var(--arena-text-muted) !important;
}

/* Rate Information */
.rate-info {
    background-color: rgba(0, 13, 40, 0.05);
    border: 1px solid var(--arena-primary);
    border-radius: 0.375rem;
    padding: 1rem;
    margin: 1rem 0;
}

.rate-info .rate-value {
    color: var(--arena-primary);
    font-weight: 600;
    font-size: 1.1em;
}

/* Document Upload Area */
.document-upload-area {
    border: 2px dashed var(--arena-border);
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    background-color: var(--arena-light-gray);
    transition: all 0.3s ease;
}

.document-upload-area:hover {
    border-color: var(--arena-primary);
    background-color: rgba(0, 13, 40, 0.05);
}

.document-upload-area.dragover {
    border-color: var(--arena-primary);
    background-color: rgba(0, 13, 40, 0.1);
}

/* File List */
.file-list-item {
    background-color: var(--arena-light-gray);
    border: 1px solid var(--arena-border);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

/* Loading States */
.loading-spinner {
    color: var(--arena-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .transaction-type-card {
        margin-bottom: 1rem;
    }
    
    .btn-group-mobile {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-group-mobile .btn {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .btn,
    .alert,
    .card-header {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
}

/* Accessibility Improvements */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--arena-primary);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --arena-primary: #000000;
        --arena-secondary: #800000;
        --arena-tertiary: #006400;
    }
}

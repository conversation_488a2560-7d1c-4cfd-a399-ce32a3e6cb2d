
/**
 * Arena Doviz Error Monitoring System
 * Captures and reports JavaScript errors, DOM issues, and API failures
 */

class ErrorMonitor {
    constructor() {
        this.errors = [];
        this.domErrors = [];
        this.apiErrors = [];
        this.init();
    }

    init() {
        this.setupGlobalErrorHandling();
        this.setupDOMErrorDetection();
        this.setupAPIErrorTracking();
        this.setupConsoleOverride();
        this.startPeriodicChecks();
        console.log('🔍 Arena Doviz Error Monitor initialized');
    }

    setupGlobalErrorHandling() {
        // Capture JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        });

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        });
    }

    setupDOMErrorDetection() {
        // Check for common DOM access patterns that might fail
        const originalGetElementById = document.getElementById;
        const originalQuerySelector = document.querySelector;
        const originalQuerySelectorAll = document.querySelectorAll;

        document.getElementById = function(id) {
            const element = originalGetElementById.call(this, id);
            if (!element) {
                window.errorMonitor.logDOMError({
                    type: 'getElementById returned null',
                    selector: `#${id}`,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    stack: new Error().stack
                });
            }
            return element;
        };

        document.querySelector = function(selector) {
            const element = originalQuerySelector.call(this, selector);
            if (!element && selector.includes('#')) {
                window.errorMonitor.logDOMError({
                    type: 'querySelector returned null',
                    selector: selector,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    stack: new Error().stack
                });
            }
            return element;
        };
    }

    setupAPIErrorTracking() {
        // Override jQuery AJAX to track API errors
        if (typeof $ !== 'undefined') {
            const originalAjax = $.ajax;
            $.ajax = function(options) {
                const originalError = options.error;
                options.error = function(xhr, status, error) {
                    window.errorMonitor.logAPIError({
                        type: 'AJAX Error',
                        url: options.url || 'unknown',
                        method: options.method || options.type || 'GET',
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText,
                        error: error,
                        timestamp: new Date().toISOString()
                    });
                    
                    if (originalError) {
                        originalError.call(this, xhr, status, error);
                    }
                };
                return originalAjax.call(this, options);
            };
        }

        // Override fetch API
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
            return originalFetch.apply(this, args)
                .then(response => {
                    if (!response.ok) {
                        window.errorMonitor.logAPIError({
                            type: 'Fetch Error',
                            url: args[0],
                            status: response.status,
                            statusText: response.statusText,
                            timestamp: new Date().toISOString()
                        });
                    }
                    return response;
                })
                .catch(error => {
                    window.errorMonitor.logAPIError({
                        type: 'Fetch Exception',
                        url: args[0],
                        error: error.message,
                        stack: error.stack,
                        timestamp: new Date().toISOString()
                    });
                    throw error;
                });
        };
    }

    setupConsoleOverride() {
        // Capture console errors
        const originalError = console.error;
        console.error = function(...args) {
            window.errorMonitor.logError({
                type: 'Console Error',
                message: args.join(' '),
                timestamp: new Date().toISOString(),
                url: window.location.href,
                stack: new Error().stack
            });
            originalError.apply(console, args);
        };
    }

    startPeriodicChecks() {
        // Check for common issues every 5 seconds
        setInterval(() => {
            this.checkForCommonIssues();
        }, 5000);
    }

    checkForCommonIssues() {
        // Check if jQuery is loaded
        if (typeof $ === 'undefined') {
            this.logError({
                type: 'Missing Dependency',
                message: 'jQuery is not loaded',
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        }

        // Check if ArenaDoviz object is available
        if (typeof ArenaDoviz === 'undefined') {
            this.logError({
                type: 'Missing Dependency',
                message: 'ArenaDoviz object is not available',
                timestamp: new Date().toISOString(),
                url: window.location.href
            });
        }

        // Check for form elements that should exist
        if (window.location.pathname.includes('/transactions/')) {
            const expectedElements = ['#transaction-form', '#customer', '#from_currency'];
            expectedElements.forEach(selector => {
                if ($(selector).length === 0) {
                    this.logDOMError({
                        type: 'Missing Expected Element',
                        selector: selector,
                        timestamp: new Date().toISOString(),
                        url: window.location.href
                    });
                }
            });
        }
    }

    logError(error) {
        this.errors.push(error);
        console.group('🚨 JavaScript Error Detected');
        console.error('Type:', error.type);
        console.error('Message:', error.message);
        console.error('URL:', error.url);
        if (error.stack) console.error('Stack:', error.stack);
        console.groupEnd();
        
        // Send to server if needed
        this.sendErrorToServer(error);
    }

    logDOMError(error) {
        this.domErrors.push(error);
        console.group('🔍 DOM Error Detected');
        console.warn('Type:', error.type);
        console.warn('Selector:', error.selector);
        console.warn('URL:', error.url);
        console.groupEnd();
    }

    logAPIError(error) {
        this.apiErrors.push(error);
        console.group('🌐 API Error Detected');
        console.error('Type:', error.type);
        console.error('URL:', error.url);
        console.error('Status:', error.status);
        console.error('Error:', error.error);
        console.groupEnd();
    }

    sendErrorToServer(error) {
        // Optional: Send errors to server for logging
        // This would require a server endpoint to receive errors
        try {
            fetch('/api/v1/errors/log/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(error)
            }).catch(() => {
                // Ignore errors when sending error logs
            });
        } catch (e) {
            // Ignore errors when sending error logs
        }
    }

    getErrorReport() {
        return {
            errors: this.errors,
            domErrors: this.domErrors,
            apiErrors: this.apiErrors,
            totalErrors: this.errors.length + this.domErrors.length + this.apiErrors.length,
            timestamp: new Date().toISOString()
        };
    }

    clearErrors() {
        this.errors = [];
        this.domErrors = [];
        this.apiErrors = [];
        console.log('🧹 Error logs cleared');
    }
}

// Initialize error monitor as soon as possible
window.errorMonitor = new ErrorMonitor();

// Add global function to get error report
window.getErrorReport = function() {
    return window.errorMonitor.getErrorReport();
};

// Add global function to clear errors
window.clearErrors = function() {
    window.errorMonitor.clearErrors();
};

/**
 * Common Transaction Form Utilities
 * Shared functions for all transaction forms
 */

class TransactionFormUtils {
    /**
     * Safely get element value with null checking
     */
    safeGetElementValue(selector) {
        try {
            const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
            return element && element.value !== undefined ? element.value : '';
        } catch (error) {
            console.warn('Error getting element value:', selector, error);
            return '';
        }
    }

    /**
     * Safely set element value with null checking
     */
    safeSetElementValue(selector, value) {
        try {
            const element = typeof selector === 'string' ? document.querySelector(selector) : selector;
            if (element && element.value !== undefined) {
                element.value = value;
                return true;
            }
            return false;
        } catch (error) {
            console.warn('Error setting element value:', selector, error);
            return false;
        }
    }

    /**
     * Safely get jQuery element value
     */
    safeGetJQueryValue(selector) {
        try {
            const $element = $(selector);
            return $element.length > 0 ? $element.val() : '';
        } catch (error) {
            console.warn('Error getting jQuery value:', selector, error);
            return '';
        }
    }

    /**
     * Safely set jQuery element value
     */
    safeSetJQueryValue(selector, value) {
        try {
            const $element = $(selector);
            if ($element.length > 0) {
                $element.val(value);
                return true;
            }
            return false;
        } catch (error) {
            console.warn('Error setting jQuery value:', selector, error);
            return false;
        }
    }

    /**
     * Wait for element to exist before accessing
     */
    async waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`Element ${selector} not found within ${timeout}ms`));
                } else {
                    setTimeout(checkElement, 100);
                }
            };
            
            checkElement();
        });
    }


    constructor() {
        this.customerBalances = [];
        this.currentBalance = 0;
    }

    /**
     * Get authentication headers for API requests
     */
    getAuthHeaders() {
        const token = ArenaDoviz.auth.getAccessToken();
        console.log('Getting auth headers, token:', token ? 'present' : 'missing');
        return {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        };
    }

    /**
     * Safely get element value with null checks
     */
    safeGetValue(selector, defaultValue = '') {
        try {
            const element = $(selector);
            if (element.length === 0) {
                console.warn(`Element not found: ${selector}`);
                return defaultValue;
            }
            return element.val() || defaultValue;
        } catch (error) {
            console.error(`Error getting value for ${selector}:`, error);
            return defaultValue;
        }
    }

    /**
     * Safely set element value with null checks
     */
    safeSetValue(selector, value) {
        try {
            const element = $(selector);
            if (element.length === 0) {
                console.warn(`Element not found: ${selector}`);
                return false;
            }
            element.val(value);
            return true;
        } catch (error) {
            console.error(`Error setting value for ${selector}:`, error);
            return false;
        }
    }

    /**
     * Check if element exists
     */
    elementExists(selector) {
        try {
            return $(selector).length > 0;
        } catch (error) {
            console.error(`Error checking element existence for ${selector}:`, error);
            return false;
        }
    }

    /**
     * Load customers for dropdown with search support
     * @param {string} targetSelector - CSS selector for the target dropdown (default: '#customer')
     * @param {string} excludeId - Customer ID to exclude from the list
     * @param {string} searchTerm - Search term to filter customers
     */
    loadCustomers(targetSelector = '#customer', excludeId = null, searchTerm = '') {
        console.log('TransactionUtils.loadCustomers called:', { targetSelector, excludeId, searchTerm });
        return new Promise((resolve, reject) => {
            let url = '/api/v1/customers/customers/';
            const params = new URLSearchParams();

            if (searchTerm) {
                params.append('search', searchTerm);
            }

            if (params.toString()) {
                url += '?' + params.toString();
            }

            console.log('Loading customers from URL:', url);
            $.ajax({
                url: url,
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    console.log('Customers loaded successfully:', data);
                    const customerSelect = $(targetSelector);
                    if (customerSelect.length === 0) {
                        console.warn(`Customer dropdown not found: ${targetSelector}`);
                        resolve(data);
                        return;
                    }

                    customerSelect.empty().append('<option value="">Select customer...</option>');

                    if (data.results && data.results.length > 0) {
                        data.results.forEach(customer => {
                            // Skip excluded customer
                            if (excludeId && customer.id == excludeId) {
                                return;
                            }

                            // Create display name with customer code for search
                            const displayName = customer.display_name || `${customer.first_name || ''} ${customer.last_name || ''}`.trim() || customer.customer_code;
                            const customerCode = customer.customer_code || '';
                            const optionText = customerCode ? `${displayName} (${customerCode})` : displayName;

                            customerSelect.append(`<option value="${customer.id}" data-customer-code="${customerCode}">${optionText}</option>`);
                        });
                    }

                    // Enable search functionality if not already enabled
                    this.enableCustomerSearch(targetSelector);

                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading customers:', xhr);
                    console.error('XHR Status:', xhr.status, 'Response:', xhr.responseText);
                    const customerSelect = $(targetSelector);
                    if (customerSelect.length > 0) {
                        customerSelect.empty().append('<option value="">Error loading customers</option>');
                    }
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Enable customer search functionality
     * @param {string} targetSelector - CSS selector for the target dropdown
     */
    enableCustomerSearch(targetSelector) {
        const customerSelect = $(targetSelector);
        if (customerSelect.length === 0) return;

        // Check if search is already enabled
        if (customerSelect.data('search-enabled')) return;

        // Mark as search enabled
        customerSelect.data('search-enabled', true);

        // Add search input if it doesn't exist
        let searchInputId = targetSelector.replace('#', '') + '-search';
        let searchInput = $('#' + searchInputId);

        if (searchInput.length === 0) {
            // Create search input
            const searchHtml = `
                <div class="mb-2">
                    <input type="text" id="${searchInputId}" class="form-control form-control-sm"
                           placeholder="Search by name or customer ID..." autocomplete="off">
                </div>
            `;
            customerSelect.before(searchHtml);
            searchInput = $('#' + searchInputId);
        }

        // Add search functionality
        let searchTimeout;
        searchInput.on('input', (e) => {
            clearTimeout(searchTimeout);
            const searchTerm = e.target.value.trim();

            searchTimeout = setTimeout(() => {
                this.loadCustomers(targetSelector, null, searchTerm);
            }, 300); // Debounce search
        });

        // Clear search when dropdown changes
        customerSelect.on('change', () => {
            if (searchInput.val()) {
                searchInput.val('');
            }
        });
    }

    /**
     * Load locations for dropdown
     */
    loadLocations() {
        console.log('TransactionUtils.loadLocations called');
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/api/v1/locations/locations/',
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    console.log('Locations loaded successfully:', data);
                    const locationSelect = $('#location');
                    locationSelect.empty().append('<option value="">Select location...</option>');
                    
                    if (data.results && data.results.length > 0) {
                        data.results.forEach(location => {
                            locationSelect.append(`<option value="${location.id}" data-code="${location.code}">${location.name}</option>`);
                        });
                    }
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading locations:', xhr);
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Load currencies for dropdown
     */
    loadCurrencies() {
        console.log('TransactionUtils.loadCurrencies called');
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/api/v1/currencies/currencies/',
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    console.log('Currencies loaded successfully:', data);
                    const fromSelect = $('#from_currency');
                    const toSelect = $('#to_currency');
                    
                    // Clear existing options
                    fromSelect.empty().append('<option value="">Select currency...</option>');
                    if (toSelect.length) {
                        toSelect.empty().append('<option value="">Select currency...</option>');
                    }
                    
                    if (data.results && data.results.length > 0) {
                        data.results.forEach(currency => {
                            const option = `<option value="${currency.id}">${currency.code} - ${currency.name}</option>`;
                            fromSelect.append(option);
                            if (toSelect.length) {
                                toSelect.append(option);
                            }
                        });
                    }
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading currencies:', xhr);
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Load transaction types
     */
    loadTransactionTypes() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/api/v1/transactions/types/',
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading transaction types:', xhr);
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Load couriers for dropdown with customer-specific support
     * @param {string} customerId - Customer ID to load specific couriers for
     */
    loadCouriers(customerId = null) {
        return new Promise((resolve, reject) => {
            const courierSelect = $('#courier');
            if (!courierSelect.length) {
                resolve([]);
                return;
            }

            // Clear current options
            courierSelect.empty().append('<option value="">Select courier...</option>');

            // If customer is selected, load customer-specific couriers first
            if (customerId) {
                this.loadCustomerSpecificCouriers(customerId)
                    .then(() => {
                        // Then load system couriers
                        return this.loadSystemCouriers();
                    })
                    .then(() => {
                        // Enable auto-complete functionality
                        this.enableCourierAutoComplete(customerId);
                        resolve();
                    })
                    .catch(reject);
            } else {
                // Load only system couriers
                this.loadSystemCouriers()
                    .then(() => {
                        resolve();
                    })
                    .catch(reject);
            }
        });
    }

    /**
     * Load customer-specific couriers
     */
    loadCustomerSpecificCouriers(customerId) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: `/api/v1/customers/customers/${customerId}/`,
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (customer) => {
                    const courierSelect = $('#courier');

                    if (customer.preferred_couriers && customer.preferred_couriers.length > 0) {
                        // Add separator for customer couriers
                        courierSelect.append('<option disabled>--- Customer Couriers ---</option>');

                        customer.preferred_couriers.forEach(courierName => {
                            // Store customer courier names as-is, but mark them as customer couriers
                            courierSelect.append(`<option value="${courierName}" data-courier-type="customer">${courierName} (Customer Courier)</option>`);
                        });
                    }
                    resolve();
                },
                error: (xhr) => {
                    console.warn('Could not load customer-specific couriers:', xhr);
                    resolve(); // Don't fail the whole process
                }
            });
        });
    }

    /**
     * Load system couriers
     */
    loadSystemCouriers() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/api/v1/accounts/users/?role=courier',
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    const courierSelect = $('#courier');

                    if (data.results && data.results.length > 0) {
                        // Add separator for system couriers
                        courierSelect.append('<option disabled>--- System Couriers ---</option>');

                        data.results.forEach(courier => {
                            const displayName = courier.display_name || `${courier.first_name || ''} ${courier.last_name || ''}`.trim() || courier.username;
                            courierSelect.append(`<option value="${courier.id}">${displayName}</option>`);
                        });
                    }
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading system couriers:', xhr);
                    const courierSelect = $('#courier');
                    courierSelect.append('<option value="" disabled>Error loading couriers</option>');
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Enable courier auto-complete functionality
     */
    enableCourierAutoComplete(customerId) {
        const courierSelect = $('#courier');

        // Convert select to input with datalist for auto-complete
        if (courierSelect.prop('tagName') === 'SELECT') {
            const currentValue = courierSelect.val();
            const inputHtml = `
                <input type="text" class="form-control" id="courier" name="courier"
                       placeholder="Type courier name or select from list..."
                       list="courier-datalist" autocomplete="off">
                <datalist id="courier-datalist"></datalist>
            `;

            courierSelect.replaceWith(inputHtml);

            // Populate datalist with options
            const datalist = $('#courier-datalist');
            courierSelect.find('option').each(function() {
                if (this.safeGetJQueryValue(this) && !$(this).prop('disabled')) {
                    datalist.append(`<option value="${$(this).text()}">`);
                }
            });

            // Add event listener for new courier creation
            $('#courier').on('blur', (e) => {
                const courierName = e.target.value.trim();
                if (courierName && !this.isExistingCourier(courierName)) {
                    this.addCustomerCourier(customerId, courierName);
                }
            });
        }
    }

    /**
     * Check if courier name already exists
     */
    isExistingCourier(courierName) {
        const datalist = $('#courier-datalist');
        let exists = false;
        datalist.find('option').each(function() {
            if (this.safeGetJQueryValue(this).toLowerCase().includes(courierName.toLowerCase())) {
                exists = true;
                return false; // break
            }
        });
        return exists;
    }

    /**
     * Add new courier to customer's preferred list
     */
    addCustomerCourier(customerId, courierName) {
        $.ajax({
            url: `/api/v1/customers/customers/${customerId}/`,
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (customer) => {
                const preferredCouriers = customer.preferred_couriers || [];
                if (!preferredCouriers.includes(courierName)) {
                    preferredCouriers.push(courierName);

                    // Update customer with new courier
                    $.ajax({
                        url: `/api/v1/customers/customers/${customerId}/`,
                        method: 'PATCH',
                        headers: this.getAuthHeaders(),
                        data: JSON.stringify({
                            preferred_couriers: preferredCouriers
                        }),
                        success: () => {
                            console.log(`Added new courier "${courierName}" to customer`);
                            // Add to datalist for immediate use
                            $('#courier-datalist').append(`<option value="${courierName} (Customer Courier)">`);
                        },
                        error: (xhr) => {
                            console.error('Error adding courier to customer:', xhr);
                        }
                    });
                }
            },
            error: (xhr) => {
                console.error('Error loading customer for courier update:', xhr);
            }
        });
    }

    /**
     * Load customer balance
     */
    loadCustomerBalance() {
        const customerId = this.safeGetJQueryValue('#customer');
        if (!customerId) {
            $('#customer-balance').html('<div class="text-muted text-center py-3"><i class="bi bi-person"></i> Select customer to view balance</div>');
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            $.ajax({
                url: `/api/v1/customers/customers/${customerId}/balance/`,
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    // The customers API returns the balance data directly as an array
                    this.customerBalances = data || [];
                    let balanceHtml = '<div class="list-group list-group-flush">';

                    if (this.customerBalances.length > 0) {
                        this.customerBalances.forEach(balance => {
                            const balanceClass = balance.balance >= 0 ? 'text-success' : 'text-danger';
                            balanceHtml += `
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>${balance.currency_code}</span>
                                    <span class="${balanceClass}">${balance.formatted_balance}</span>
                                </div>
                            `;
                        });
                    } else {
                        balanceHtml += '<div class="list-group-item text-muted text-center">No balance records found</div>';
                    }

                    balanceHtml += '</div>';
                    $('#customer-balance').html(balanceHtml);
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading customer balance:', xhr);
                    $('#customer-balance').html('<div class="text-danger text-center py-3"><i class="bi bi-exclamation-triangle"></i> Error loading balance</div>');
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Load exchange rates for location
     */
    loadExchangeRates(locationCode = null) {
        // Get location code from the selected location if not provided
        let location = locationCode;
        if (!location) {
            const locationSelect = $('#location');
            const selectedOption = locationSelect.find('option:selected');
            location = selectedOption.data('code') || selectedOption.text();
        }

        if (!location) {
            return Promise.resolve([]);
        }

        return new Promise((resolve, reject) => {
            $.ajax({
                url: `/api/v1/currencies/rates/current/?location=${location}`,
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error loading exchange rates:', xhr);
                    reject(xhr);
                }
            });
        });
    }

    /**
     * Handle file selection for document upload
     */
    handleFileSelection() {
        const files = $('#document_files')[0].files;
        if (files.length > 0) {
            let fileListHtml = '';
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileListHtml += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-file-earmark"></i>
                            <span class="ms-2">${file.name}</span>
                            <small class="text-muted ms-2">(${fileSize} MB)</small>
                        </div>
                    </div>
                `;
            }
            $('#file-list').html(fileListHtml);
            $('#uploaded-files-preview').show();
        } else {
            $('#uploaded-files-preview').hide();
        }
    }

    /**
     * Upload documents for a transaction
     */
    uploadDocuments(transactionId, files) {
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            const documentType = $('#document_type').val() || 'other';

            // Add files to form data
            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            // Add transaction ID and document type
            formData.append('transaction_id', transactionId);
            formData.append('document_type', documentType);

            $.ajax({
                url: '/api/v1/transactions/documents/bulk_upload/',
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
                },
                data: formData,
                processData: false,
                contentType: false,
                success: (response) => {
                    console.log('Documents uploaded successfully:', response);
                    resolve(response);
                },
                error: (xhr) => {
                    console.error('Error uploading documents:', xhr);
                    let errorMessage = 'Failed to upload documents';

                    if (xhr.responseJSON) {
                        if (xhr.responseJSON.error) {
                            errorMessage = xhr.responseJSON.error;
                        } else if (xhr.responseJSON.errors) {
                            errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                        }
                    }

                    reject(new Error(errorMessage));
                }
            });
        });
    }

    /**
     * Show alert message
     */
    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        $('.alert').remove();
        $('.row').first().before(alertHtml);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }

    /**
     * Process courier field to handle both system couriers (UUIDs) and customer couriers (names)
     */
    processCourierField(data) {
        const courierValue = data.courier;
        const courierSelect = $('#courier');
        const selectedOption = courierSelect.find(`option[value="${courierValue}"]`);

        if (selectedOption.length > 0) {
            const courierType = selectedOption.data('courier-type');

            if (courierType === 'customer') {
                // For customer couriers, we need to create a temporary courier record or handle it differently
                // For now, we'll send the name and let the backend handle it
                data.courier_name = courierValue;
                data.courier_type = 'customer';
                delete data.courier; // Remove the courier field since it's not a UUID
            } else {
                // System courier - courierValue is already a UUID, keep as is
                data.courier_type = 'system';
            }
        } else {
            // If no option found, it might be a manually entered courier name
            // Check if it's a valid UUID format
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(courierValue)) {
                // It's a name, not a UUID
                data.courier_name = courierValue;
                data.courier_type = 'customer';
                delete data.courier;
            }
        }

        return data;
    }

    /**
     * Submit transaction form
     */
    submitTransaction(form, action = 'save') {
        return new Promise((resolve, reject) => {
            const formData = new FormData(form);
        
        // Add defensive checks for form elements
        if (!form || !form.elements) {
            console.error('Invalid form or form elements not available');
            return;
        }
            const data = {};

            // Convert FormData to object
            for (let [key, value] of formData.entries()) {
                if (value !== '' && value !== null) {
                    data[key] = value;
                }
            }

            // Set status based on action
            data.status = action === 'submit' ? 'pending' : 'draft';

            // Process courier field - handle both system couriers (UUIDs) and customer couriers (names)
            if (data.courier) {
                data = this.processCourierField(data);
            }

            // Ensure required fields are present
            if (!data.transaction_type) {
                // Try to get from hidden field or transaction type code
                const transactionTypeCode = data.transaction_type_code;
                if (transactionTypeCode) {
                    // We'll need to get the transaction type ID from the code
                    this.loadTransactionTypes()
                        .then((types) => {
                            const transactionType = types.results.find(t => t.code === transactionTypeCode);
                            if (transactionType) {
                                data.transaction_type = transactionType.id;
                                this.performTransactionSubmission(data, resolve, reject);
                            } else {
                                reject(new Error('Invalid transaction type'));
                            }
                        })
                        .catch(reject);
                    return;
                }
            }

            this.performTransactionSubmission(data, resolve, reject);
        });
    }

    /**
     * Perform the actual transaction submission
     */
    performTransactionSubmission(data, resolve, reject) {
        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'POST',
            headers: this.getAuthHeaders(),
            data: JSON.stringify(data),
            success: (response) => {
                resolve(response);
            },
            error: (xhr) => {
                console.error('Error submitting transaction:', xhr);
                let errorMessage = 'Failed to submit transaction';

                if (xhr.responseJSON) {
                    if (xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    } else if (xhr.responseJSON.errors) {
                        errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                    } else {
                        // Handle field-specific errors
                        const errors = [];
                        for (let field in xhr.responseJSON) {
                            if (Array.isArray(xhr.responseJSON[field])) {
                                errors.push(`${field}: ${xhr.responseJSON[field].join(', ')}`);
                            } else {
                                errors.push(`${field}: ${xhr.responseJSON[field]}`);
                            }
                        }
                        if (errors.length > 0) {
                            errorMessage = errors.join('<br>');
                        }
                    }
                }

                reject(new Error(errorMessage));
            }
        });
    }

    /**
     * Format currency amount
     */
    formatCurrency(amount, currency, precision = 2) {
        if (amount === null || amount === undefined) return '-';

        const symbols = {
            'USD': '$',
            'AED': 'د.إ',
            'IRR': '﷼'
        };

        const symbol = symbols[currency] || currency;
        const formatted = parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: precision,
            maximumFractionDigits: precision
        });

        return `${symbol} ${formatted}`;
    }

    /**
     * Calculate commission for a transaction
     * @param {number} amount - Transaction amount
     * @param {string} transactionType - Type of transaction
     * @param {string} fromCurrency - Source currency
     * @param {string} toCurrency - Target currency
     * @param {string} location - Location code
     * @returns {Promise} Promise that resolves with commission calculation
     */
    calculateCommission(amount, transactionType, fromCurrency, toCurrency, location) {
        return new Promise((resolve, reject) => {
            // For now, use a simple commission calculation
            // This should be replaced with actual API call to commission service

            const commissionRates = {
                'EXCHANGE': 0.002, // 0.2%
                'TRANSFER': 0.001, // 0.1%
                'DEPOSIT': 0.0005, // 0.05%
                'WITHDRAWAL': 0.0005, // 0.05%
                'REMITTANCE': 0.003, // 0.3%
                'ADJUSTMENT': 0 // No commission
            };

            const rate = commissionRates[transactionType] || 0.001;
            const commissionAmount = amount * rate;

            const result = {
                commission_amount: commissionAmount,
                commission_rate: rate,
                commission_percentage: rate * 100,
                base_amount: amount,
                total_amount: amount + commissionAmount,
                currency: fromCurrency
            };

            // Simulate API delay
            setTimeout(() => {
                resolve(result);
            }, 100);
        });
    }

    /**
     * Get current exchange rate
     * @param {string} fromCurrency - Source currency code
     * @param {string} toCurrency - Target currency code
     * @param {string} location - Location code
     * @param {string} rateType - Rate type (buy/sell/mid)
     * @returns {Promise} Promise that resolves with exchange rate
     */
    getExchangeRate(fromCurrency, toCurrency, location, rateType = 'mid') {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: `/api/v1/currencies/rates/current/?from_currency=${fromCurrency}&to_currency=${toCurrency}&location=${location}&rate_type=${rateType}`,
                method: 'GET',
                headers: this.getAuthHeaders(),
                success: (data) => {
                    resolve(data);
                },
                error: (xhr) => {
                    console.error('Error getting exchange rate:', xhr);
                    reject(xhr);
                }
            });
        });
    }
}

// Create global instance
console.log('Creating TransactionUtils global instance');
window.TransactionUtils = new TransactionFormUtils();
console.log('TransactionUtils created:', window.TransactionUtils);

// Global functions for backward compatibility
window.calculateCommission = function(amount, transactionType, fromCurrency, toCurrency, location) {
    return TransactionUtils.calculateCommission(amount, transactionType, fromCurrency, toCurrency, location);
};

window.getExchangeRate = function(fromCurrency, toCurrency, location, rateType) {
    return TransactionUtils.getExchangeRate(fromCurrency, toCurrency, location, rateType);
};

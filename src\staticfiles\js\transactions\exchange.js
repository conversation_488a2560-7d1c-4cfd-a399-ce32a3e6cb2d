/**
 * Currency Exchange Transaction Form Handler
 * Handles form validation, rate calculation, and submission for currency exchange transactions
 */

class ExchangeTransactionForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'EXCHANGE';
        this.currentRates = {};

        // Only initialize if form exists
        if (this.form) {
            this.init();
        } else {
            console.warn('Transaction form not found, skipping ExchangeTransactionForm initialization');
        }
    }

    init() {
        this.loadFormData();
        this.bindEvents();
        this.setupValidation();
    }

    loadFormData() {
        // Load transaction types and set the exchange type
        this.loadTransactionTypes();
        TransactionUtils.loadCustomers();
        TransactionUtils.loadLocations();
        TransactionUtils.loadCurrencies();
    }

    bindEvents() {
        // Customer selection
        $('#customer').on('change', () => {
            TransactionUtils.loadCustomerBalance();
        });

        // Currency and location changes
        $('#from_currency, #to_currency, #location').on('change', () => {
            this.loadCurrentRates();
        });

        // Amount and rate calculations
        $('#from_amount, #exchange_rate').on('input', () => {
            this.calculateToAmount();
            this.updateTransactionPreview();
        });

        // Get current rate button
        $('#get-current-rate').on('click', () => {
            this.getCurrentRate();
        });

        // Form submission
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter?.dataset?.action || 'save';
            this.submitTransaction(action);
        });

        // File upload handling
        $('#document_files').on('change', () => {
            TransactionUtils.handleFileSelection();
        });

        // Real-time form validation and preview updates
        $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', () => {
            this.updateTransactionPreview();
        });
    }

    setupValidation() {
        // Custom validation rules for exchange transactions
        this.form.addEventListener('submit', (e) => {
            if (!this.validateExchangeForm()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    validateExchangeForm() {
        let isValid = true;
        const errors = [];

        // Validate currencies are different
        const fromCurrency = $('#from_currency').val();
        const toCurrency = $('#to_currency').val();

        if (fromCurrency && toCurrency && fromCurrency === toCurrency) {
            errors.push('From and To currencies must be different for exchange transactions');
            isValid = false;
        }

        // Validate amount
        const fromAmount = parseFloat($('#from_amount').val());
        if (fromAmount <= 0) {
            errors.push('Amount must be greater than zero');
            isValid = false;
        }

        if (!isValid) {
            TransactionUtils.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const exchangeType = data.results.find(type => type.code === 'EXCHANGE');
                if (exchangeType) {
                    // Set hidden field for transaction type
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: exchangeType.id
                    }).appendTo(this.form);
                }
            },
            error: () => {
                TransactionUtils.showAlert('danger', 'Error loading transaction types');
            }
        });
    }

    // Removed - now using TransactionUtils.loadCustomers()

    // Removed - now using TransactionUtils.loadLocations()

    // Removed - now using TransactionUtils.loadCurrencies()



    // Removed - now using TransactionUtils.loadCustomerBalance()

    loadCurrentRates() {
        const fromCurrency = $('#from_currency').val();
        const toCurrency = $('#to_currency').val();
        const locationSelect = $('#location');
        const locationCode = locationSelect.find('option:selected').data('code');

        if (!fromCurrency || !toCurrency || !locationCode) {
            return;
        }

        TransactionUtils.loadExchangeRates(locationCode)
            .then((data) => {
                this.currentRates = {};

                if (data && data.length > 0) {
                    data.forEach(rate => {
                        const key = `${rate.from_currency}_${rate.to_currency}`;
                        this.currentRates[key] = {
                            buy_rate: rate.buy_rate,
                            sell_rate: rate.sell_rate,
                            from_currency: rate.from_currency,
                            to_currency: rate.to_currency
                        };
                    });
                }

                // Update rate display
                this.updateRateDisplay();
            })
            .catch(() => {
                console.warn('Error loading current rates');
                $('#rate-info').hide();
            });
    }

    updateRateDisplay() {
        // Use safe methods to get currency values
        const fromCurrency = TransactionUtils.safeGetValue('#from_currency');
        const toCurrency = TransactionUtils.safeGetValue('#to_currency');

        if (!fromCurrency || !toCurrency) {
            if (TransactionUtils.elementExists('#rate-info')) {
                $('#rate-info').hide();
            }
            return;
        }

        // Get currency codes for display
        const fromCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
        const toCurrencyCode = $('#to_currency option:selected').text().split(' - ')[0];
        const rateKey = `${fromCurrencyCode}_${toCurrencyCode}`;

        if (this.currentRates[rateKey]) {
            const rate = this.currentRates[rateKey];
            $('#rate-info').show();
            $('#rate-info-text').html(`
                <strong>Current Rates:</strong><br>
                Buy: 1 ${rate.from_currency} = ${rate.buy_rate} ${rate.to_currency}<br>
                Sell: 1 ${rate.from_currency} = ${rate.sell_rate} ${rate.to_currency}
            `);
        } else {
            $('#rate-info').hide();
        }
    }

    getCurrentRate() {
        const fromCurrency = $('#from_currency').val();
        const toCurrency = $('#to_currency').val();

        if (!fromCurrency || !toCurrency) {
            return;
        }

        // Get currency codes for rate lookup
        const fromCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];
        const toCurrencyCode = $('#to_currency option:selected').text().split(' - ')[0];
        const rateKey = `${fromCurrencyCode}_${toCurrencyCode}`;

        if (this.currentRates[rateKey]) {
            // Use sell rate for customer exchanges (customer is selling to us)
            const rate = this.currentRates[rateKey].sell_rate;
            $('#exchange_rate').val(rate);
            this.calculateToAmount();
            this.updateTransactionPreview();
        } else {
            this.loadCurrentRates();
        }
    }

    calculateToAmount() {
        const fromAmount = parseFloat($('#from_amount').val()) || 0;
        const exchangeRate = parseFloat($('#exchange_rate').val()) || 0;

        if (fromAmount > 0 && exchangeRate > 0) {
            const toAmount = fromAmount * exchangeRate;
            $('#to_amount').val(toAmount.toFixed(6));
        } else {
            $('#to_amount').val('');
        }
    }



    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            fromCurrency: $('#from_currency option:selected').text(),
            toCurrency: $('#to_currency option:selected').text(),
            fromAmount: formData.get('from_amount'),
            toAmount: formData.get('to_amount'),
            exchangeRate: formData.get('exchange_rate'),
            commission: formData.get('commission_amount')
        };

        let previewHtml = '<div class="list-group list-group-flush">';

        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>Customer:</strong> ${preview.customer}</div>`;
        }

        if (preview.fromAmount && preview.fromCurrency && preview.fromCurrency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Exchange:</strong> ${preview.fromAmount} ${preview.fromCurrency.split(' - ')[0]}</div>`;
        }

        if (preview.toAmount && preview.toCurrency && preview.toCurrency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Receive:</strong> ${preview.toAmount} ${preview.toCurrency.split(' - ')[0]}</div>`;
        }

        if (preview.exchangeRate) {
            previewHtml += `<div class="list-group-item"><strong>Rate:</strong> ${preview.exchangeRate}</div>`;
        }

        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Service Fee:</strong> ${preview.commission}</div>`;
        }

        previewHtml += '</div>';

        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }

        $('#transaction-preview').html(previewHtml);
    }

    submitTransaction(action) {
        if (!this.validateExchangeForm()) {
            return;
        }

        // Set default delivery method for exchange
        $('<input>').attr({
            type: 'hidden',
            id: 'delivery_method',
            name: 'delivery_method',
            value: 'in_person'
        }).appendTo(this.form);

        TransactionUtils.submitTransaction(this.form, action)
            .then((response) => {
                const transactionId = response.id;
                const files = $('#document_files')[0].files;

                if (files.length > 0) {
                    TransactionUtils.uploadDocuments(transactionId, files)
                        .then(() => {
                            TransactionUtils.showAlert('success', 'Exchange transaction and documents uploaded successfully');
                            setTimeout(() => {
                                window.location.href = '/transactions/type/EXCHANGE/';
                            }, 2000);
                        })
                        .catch((error) => {
                            TransactionUtils.showAlert('warning', `Exchange transaction created but document upload failed: ${error.message}`);
                            setTimeout(() => {
                                window.location.href = '/transactions/type/EXCHANGE/';
                            }, 5000);
                        });
                } else {
                    TransactionUtils.showAlert('success', 'Exchange transaction created successfully');
                    setTimeout(() => {
                        window.location.href = '/transactions/type/EXCHANGE/';
                    }, 2000);
                }
            })
            .catch((error) => {
                TransactionUtils.showAlert('danger', `Error creating exchange transaction: ${error.message}`);
            });
    }

    // Removed - now using TransactionUtils.uploadDocuments()

    // Removed - now using TransactionUtils.handleFileSelection()

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    // Removed - now using TransactionUtils.showAlert()
}

// Initialize when DOM is ready
$(document).ready(() => {
    new ExchangeTransactionForm();
});

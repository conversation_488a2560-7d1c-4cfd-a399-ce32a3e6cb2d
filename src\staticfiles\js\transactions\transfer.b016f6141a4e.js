/**
 * Money Transfer Transaction Form Handler
 * Handles form validation, recipient management, and submission for money transfer transactions
 */

class TransferTransactionForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'TRANSFER';

        // Only initialize if form exists
        if (this.form) {
            this.init();
        } else {
            console.warn('Transaction form not found, skipping TransferTransactionForm initialization');
        }
    }

    init() {
        this.loadFormData();
        this.bindEvents();
        this.setupValidation();
    }

    loadFormData() {
        this.loadTransactionTypes();
        TransactionUtils.loadCustomers();
        TransactionUtils.loadLocations();
        TransactionUtils.loadCurrencies();
    }

    bindEvents() {
        // Customer selection
        $('#customer').on('change', () => {
            TransactionUtils.loadCustomerBalance();
        });

        // Recipient customer selection
        $('#recipient_customer').on('change', () => {
            this.loadRecipientBalance();
        });

        // Amount changes
        $('#from_amount').on('input', () => {
            this.checkSufficientBalance();
            this.updateTransactionPreview();
        });

        // Currency changes
        $('#from_currency').on('change', () => {
            this.checkSufficientBalance();
        });

        // Transfer type changes
        $('#transfer_type').on('change', () => {
            this.toggleTransferTypeFields();
        });

        // Delivery method changes
        $('#delivery_method').on('change', () => {
            this.toggleDeliveryFields();
        });

        // Multi-step checkbox
        $('#is_multi_step').on('change', () => {
            this.toggleMultiStepSection();
        });

        // Form submission
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter?.dataset?.action || 'save';
            this.submitTransaction(action);
        });

        // File upload handling
        $('#document_files').on('change', () => {
            this.handleFileSelection();
        });

        // Real-time form validation and preview updates
        $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', () => {
            this.updateTransactionPreview();
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validateTransferForm()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    validateTransferForm() {
        let isValid = true;
        const errors = [];

        // Validate sender and recipient are different
        const senderId = $('#customer').val();
        const recipientId = $('#recipient_customer').val();
        
        if (senderId && recipientId && senderId === recipientId) {
            errors.push('Sender and recipient must be different customers');
            isValid = false;
        }

        // Validate amount
        const amount = parseFloat($('#from_amount').val());
        if (amount <= 0) {
            errors.push('Transfer amount must be greater than zero');
            isValid = false;
        }

        // Validate delivery method specific fields
        const deliveryMethod = $('#delivery_method').val();
        if (deliveryMethod === 'swift' && !$('#swift_code').val()) {
            errors.push('SWIFT code is required for SWIFT transfers');
            isValid = false;
        }

        if (deliveryMethod === 'bank_transfer' && !$('#bank_details').val()) {
            errors.push('Bank details are required for bank transfers');
            isValid = false;
        }

        // Validate multi-step fields
        if ($('#is_multi_step').is(':checked')) {
            const stepNumber = parseInt($('#step_number').val());
            const totalSteps = parseInt($('#total_steps').val());
            
            if (stepNumber <= 0 || totalSteps <= 0) {
                errors.push('Step numbers must be greater than zero');
                isValid = false;
            }
            
            if (stepNumber > totalSteps) {
                errors.push('Step number cannot be greater than total steps');
                isValid = false;
            }
        }

        if (!isValid) {
            this.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const transferType = data.results.find(type => type.code === 'TRANSFER');
                if (transferType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: transferType.id
                    }).appendTo(this.form);
                }
            },
            error: () => {
                this.showAlert('danger', 'Error loading transaction types');
            }
        });
    }

    loadCustomers() {
        $.ajax({
            url: '/api/v1/customers/customers/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const customerSelect = $('#customer');
                const recipientSelect = $('#recipient_customer');
                
                customerSelect.empty().append('<option value="">Select sender...</option>');
                recipientSelect.empty().append('<option value="">Select recipient...</option>');
                
                data.results.forEach(customer => {
                    const option = `<option value="${customer.id}">${customer.first_name} ${customer.last_name}</option>`;
                    customerSelect.append(option);
                    recipientSelect.append(option);
                });
            },
            error: () => {
                this.showAlert('danger', 'Error loading customers');
            }
        });
    }

    loadLocations() {
        $.ajax({
            url: '/api/v1/locations/locations/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const locationSelect = $('#location');
                const destinationSelect = $('#destination_location');
                
                locationSelect.empty().append('<option value="">Select location...</option>');
                destinationSelect.empty().append('<option value="">Select destination...</option>');
                
                data.results.forEach(location => {
                    const option = `<option value="${location.id}">${location.name}</option>`;
                    locationSelect.append(option);
                    destinationSelect.append(option);
                });
            },
            error: () => {
                this.showAlert('danger', 'Error loading locations');
            }
        });
    }

    loadCurrencies() {
        $.ajax({
            url: '/api/v1/currencies/currencies/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const currencySelect = $('#from_currency');
                currencySelect.empty().append('<option value="">Select currency...</option>');
                
                data.results.forEach(currency => {
                    currencySelect.append(`<option value="${currency.id}">${currency.code} - ${currency.name}</option>`);
                });
                
                // Set to_currency same as from_currency for transfers
                $('#from_currency').on('change', () => {
                    const selectedCurrency = $('#from_currency').val();
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'to_currency',
                        name: 'to_currency',
                        value: selectedCurrency
                    }).appendTo(this.form);
                    
                    // Set to_amount same as from_amount for transfers
                    $('#from_amount').on('input', () => {
                        $('#to_amount').val($('#from_amount').val());
                    });
                });
            },
            error: () => {
                this.showAlert('danger', 'Error loading currencies');
            }
        });
    }

    loadCustomerBalance() {
        const customerId = $('#customer').val();
        if (!customerId) {
            $('#customer-balance').html('<div class="text-muted text-center py-3"><i class="bi bi-person"></i> Select sender to view balance</div>');
            return;
        }

        $.ajax({
            url: `/api/v1/transactions/api/customer/${customerId}/balance/`,
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                this.customerBalances = data.balances || [];
                let balanceHtml = '<div class="list-group list-group-flush">';

                if (this.customerBalances.length > 0) {
                    this.customerBalances.forEach(balance => {
                        const balanceClass = balance.amount >= 0 ? 'text-success' : 'text-danger';
                        balanceHtml += `
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <span>${balance.currency_code}</span>
                                <span class="${balanceClass}">${balance.formatted_amount}</span>
                            </div>
                        `;
                    });
                } else {
                    balanceHtml += '<div class="list-group-item text-muted text-center">No balance records found</div>';
                }

                balanceHtml += '</div>';
                $('#customer-balance').html(balanceHtml);
                this.checkSufficientBalance();
            },
            error: () => {
                $('#customer-balance').html('<div class="text-danger text-center py-3"><i class="bi bi-exclamation-triangle"></i> Error loading balance</div>');
            }
        });
    }

    checkSufficientBalance() {
        const transferAmount = parseFloat($('#from_amount').val()) || 0;
        const selectedCurrencyId = $('#from_currency').val();

        if (!this.customerBalances || !selectedCurrencyId || transferAmount <= 0) {
            return;
        }

        const balance = this.customerBalances.find(b => b.currency_id == selectedCurrencyId);
        const availableBalance = balance ? balance.amount : 0;

        // Show warning if insufficient balance
        let warningHtml = '';
        if (transferAmount > availableBalance) {
            warningHtml = `
                <div class="alert alert-warning mt-2">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Warning:</strong> Transfer amount (${transferAmount}) exceeds available balance (${availableBalance}).
                </div>
            `;
        }

        // Update or remove warning
        $('#balance-warning').remove();
        if (warningHtml) {
            $('#from_amount').closest('.mb-3').append('<div id="balance-warning">' + warningHtml + '</div>');
        }
    }

    loadRecipientBalance() {
        const recipientId = $('#recipient_customer').val();
        if (!recipientId) {
            return;
        }

        // Optional: Show recipient balance in preview or separate section
        console.log('Recipient selected:', recipientId);
    }

    toggleTransferTypeFields() {
        const transferType = $('#transfer_type').val();
        const recipientField = $('#recipient_customer').closest('.col-md-6');

        switch (transferType) {
            case 'internal':
                // Show recipient customer field for internal transfers
                recipientField.show();
                $('#recipient_customer').prop('required', true);
                break;
            case 'external':
            case 'international':
                // Hide recipient customer field for external transfers
                recipientField.hide();
                $('#recipient_customer').prop('required', false);
                break;
            default:
                recipientField.show();
                $('#recipient_customer').prop('required', true);
        }
    }

    toggleDeliveryFields() {
        const deliveryMethod = $('#delivery_method').val();

        // Hide all conditional fields
        $('#swift-details, #bank-details').hide();

        // Show relevant fields based on delivery method
        switch (deliveryMethod) {
            case 'swift':
                $('#swift-details').show();
                break;
            case 'bank_transfer':
                $('#bank-details').show();
                break;
        }
    }

    toggleMultiStepSection() {
        const isMultiStep = $('#is_multi_step').is(':checked');
        if (isMultiStep) {
            $('#multi-step-section').show();
        } else {
            $('#multi-step-section').hide();
        }
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            sender: $('#customer option:selected').text(),
            recipient: $('#recipient_customer option:selected').text(),
            currency: $('#from_currency option:selected').text(),
            amount: formData.get('from_amount'),
            commission: formData.get('commission_amount'),
            deliveryMethod: $('#delivery_method option:selected').text(),
            trackingCode: formData.get('tracking_code')
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.sender && preview.sender !== 'Select sender...') {
            previewHtml += `<div class="list-group-item"><strong>Sender:</strong> ${preview.sender}</div>`;
        }
        
        if (preview.recipient && preview.recipient !== 'Select recipient...') {
            previewHtml += `<div class="list-group-item"><strong>Recipient:</strong> ${preview.recipient}</div>`;
        }
        
        if (preview.amount && preview.currency && preview.currency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Amount:</strong> ${preview.amount} ${preview.currency.split(' - ')[0]}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Transfer Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.deliveryMethod && preview.deliveryMethod !== 'Select transfer method...') {
            previewHtml += `<div class="list-group-item"><strong>Method:</strong> ${preview.deliveryMethod}</div>`;
        }
        
        if (preview.trackingCode) {
            previewHtml += `<div class="list-group-item"><strong>Tracking:</strong> ${preview.trackingCode}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    submitTransaction(action) {
        if (!this.validateTransferForm()) {
            return;
        }

        const formData = new FormData(this.form);
        const data = Object.fromEntries(formData.entries());
        
        // Set status based on action (transfers typically require approval)
        data.status = action === 'submit' ? 'pending' : 'draft';
        
        // Set to_currency and to_amount same as from for transfers
        data.to_currency = data.from_currency;
        data.to_amount = data.from_amount;
        data.exchange_rate = 1; // No exchange for same currency transfers

        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'POST',
            headers: this.getAuthHeaders(),
            data: JSON.stringify(data),
            success: (response) => {
                const transactionId = response.id;

                // Upload documents if any
                const files = $('#document_files')[0].files;
                if (files.length > 0) {
                    this.uploadDocuments(transactionId, files);
                } else {
                    this.showAlert('success', 'Money transfer created successfully');
                    setTimeout(() => {
                        window.location.href = '/transactions/type/TRANSFER/';
                    }, 2000);
                }
            },
            error: (xhr) => {
                const errors = xhr.responseJSON;
                let errorMessage = 'Error creating money transfer';

                if (errors) {
                    errorMessage += ':<br>';
                    for (let field in errors) {
                        errorMessage += `${field}: ${errors[field]}<br>`;
                    }
                }

                this.showAlert('danger', errorMessage);
            }
        });
    }

    uploadDocuments(transactionId, files) {
        const formData = new FormData();
        const documentType = $('#document_type').val();

        for (let i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        formData.append('document_type', documentType);
        formData.append('transaction', transactionId);

        $.ajax({
            url: '/api/v1/transactions/documents/',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken()
            },
            data: formData,
            processData: false,
            contentType: false,
            success: () => {
                this.showAlert('success', 'Money transfer and documents uploaded successfully');
                setTimeout(() => {
                    window.location.href = '/transactions/type/TRANSFER/';
                }, 2000);
            },
            error: () => {
                this.showAlert('warning', 'Money transfer created but document upload failed');
                setTimeout(() => {
                    window.location.href = '/transactions/type/TRANSFER/';
                }, 5000);
            }
        });
    }

    handleFileSelection() {
        const files = $('#document_files')[0].files;
        if (files.length > 0) {
            let fileListHtml = '';
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const fileSize = (file.size / 1024 / 1024).toFixed(2);
                fileListHtml += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-file-earmark"></i>
                            <span class="ms-2">${file.name}</span>
                            <small class="text-muted ms-2">(${fileSize} MB)</small>
                        </div>
                    </div>
                `;
            }
            $('#file-list').html(fileListHtml);
            $('#uploaded-files-preview').show();
        } else {
            $('#uploaded-files-preview').hide();
        }
    }

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        $('.alert').remove();
        $('.row').first().before(alertHtml);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
}

// Initialize when DOM is ready
$(document).ready(() => {
    new TransferTransactionForm();
});

/**
 * Cash Withdrawal Transaction Form Handler
 */

class WithdrawalTransactionForm {
    constructor() {
        this.form = document.getElementById('transaction-form');
        this.transactionTypeCode = 'WITHDRAWAL';

        // Only initialize if form exists
        if (this.form) {
            this.init();
        } else {
            console.warn('Transaction form not found, skipping WithdrawalTransactionForm initialization');
        }
    }

    init() {
        this.loadFormData();
        this.bindEvents();
        this.setupValidation();
        this.setCurrentUser();
        this.setupCurrencyHandlers();
    }

    loadFormData() {
        this.loadTransactionTypes();
        TransactionUtils.loadCustomers();
        TransactionUtils.loadLocations();
        TransactionUtils.loadCurrencies();
        TransactionUtils.loadCouriers();
    }

    bindEvents() {
        $('#customer').on('change', () => {
            const customerId = $('#customer').val();
            TransactionUtils.loadCustomerBalance().then(() => {
                this.updateAvailableBalance();
            });

            // Reload couriers with customer-specific options
            if (customerId) {
                TransactionUtils.loadCouriers(customerId);
            }
        });

        $('#from_currency').on('change', () => {
            this.updateAvailableBalance();
        });

        $('#from_amount').on('input', () => {
            this.checkSufficientBalance();
            this.updateTransactionPreview();
        });

        $('#withdrawal_method').on('change', () => {
            this.toggleWithdrawalFields();
        });

        $('#delivery_method').on('change', () => {
            this.toggleCourierField();
        });

        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            const action = e.submitter?.dataset?.action || 'save';
            this.submitTransaction(action);
        });

        $('#document_files').on('change', () => {
            TransactionUtils.handleFileSelection();
        });

        $('#transaction-form input, #transaction-form select, #transaction-form textarea').on('change input', () => {
            this.updateTransactionPreview();
        });
    }

    setupValidation() {
        this.form.addEventListener('submit', (e) => {
            if (!this.validateWithdrawalForm()) {
                e.preventDefault();
                e.stopPropagation();
            }
        });
    }

    validateWithdrawalForm() {
        let isValid = true;
        const errors = [];

        const amount = parseFloat($('#from_amount').val());
        if (amount <= 0) {
            errors.push('Withdrawal amount must be greater than zero');
            isValid = false;
        }

        if (!$('#customer_id_verified').is(':checked')) {
            errors.push('Customer ID verification is required');
            isValid = false;
        }

        if (!$('#signature_verified').is(':checked')) {
            errors.push('Signature verification is required');
            isValid = false;
        }

        const withdrawalMethod = $('#withdrawal_method').val();
        if (withdrawalMethod === 'bank_transfer' && !$('#bank_account').val()) {
            errors.push('Bank account details are required for bank transfers');
            isValid = false;
        }

        if (!isValid) {
            TransactionUtils.showAlert('danger', errors.join('<br>'));
        }

        return isValid;
    }

    loadTransactionTypes() {
        $.ajax({
            url: '/api/v1/transactions/types/',
            method: 'GET',
            headers: this.getAuthHeaders(),
            success: (data) => {
                const withdrawalType = data.results.find(type => type.code === 'WITHDRAWAL');
                if (withdrawalType) {
                    $('<input>').attr({
                        type: 'hidden',
                        id: 'transaction_type',
                        name: 'transaction_type',
                        value: withdrawalType.id
                    }).appendTo(this.form);
                }
            }
        });
    }

    // Removed - now using TransactionUtils.loadCustomers()

    // Removed - now using TransactionUtils.loadLocations()

    // Removed - now using TransactionUtils.loadCurrencies()
    // Additional currency setup for withdrawal
    setupCurrencyHandlers() {
        $('#from_currency').on('change', () => {
            const selectedCurrency = $('#from_currency').val();
            // Remove existing hidden field if any
            $('#to_currency').remove();

            if (selectedCurrency) {
                $('<input>').attr({
                    type: 'hidden',
                    id: 'to_currency',
                    name: 'to_currency',
                    value: selectedCurrency
                }).appendTo(this.form);
            }

            $('#from_amount').off('input.withdrawal').on('input.withdrawal', () => {
                $('#to_amount').val($('#from_amount').val());
            });
        });
    }

    // Removed - now using TransactionUtils.loadCustomerBalance()

    updateAvailableBalance() {
        const selectedCurrencyId = $('#from_currency').val();
        if (!selectedCurrencyId || !TransactionUtils.customerBalances) {
            $('#available_balance').val('');
            return;
        }

        // Get currency code from the selected option
        const selectedCurrencyCode = $('#from_currency option:selected').text().split(' - ')[0];

        // Find balance by currency code
        const balance = TransactionUtils.customerBalances.find(b => b.currency_code === selectedCurrencyCode);
        if (balance) {
            $('#available_balance').val(balance.formatted_balance);
            this.currentBalance = balance.balance;
        } else {
            $('#available_balance').val('0.00');
            this.currentBalance = 0;
        }
    }

    checkSufficientBalance() {
        const withdrawalAmount = parseFloat($('#from_amount').val()) || 0;
        const alertElement = $('#insufficient-balance-alert');
        
        if (this.currentBalance !== undefined && withdrawalAmount > this.currentBalance) {
            alertElement.show();
        } else {
            alertElement.hide();
        }
    }

    toggleWithdrawalFields() {
        const withdrawalMethod = $('#withdrawal_method').val();
        $('#bank-transfer-details').toggle(withdrawalMethod === 'bank_transfer');
    }

    toggleCourierField() {
        const deliveryMethod = $('#delivery_method').val();
        const courierField = $('#courier-field');

        if (deliveryMethod === 'courier') {
            courierField.show();
            $('#courier').attr('required', true);
        } else {
            courierField.hide();
            $('#courier').attr('required', false);
        }
    }

    setCurrentUser() {
        $('#authorized_by').val('Current User');
    }

    updateTransactionPreview() {
        const formData = new FormData(this.form);
        const preview = {
            customer: $('#customer option:selected').text(),
            currency: $('#from_currency option:selected').text(),
            amount: formData.get('from_amount'),
            commission: formData.get('commission_amount'),
            method: $('#withdrawal_method option:selected').text(),
            idVerified: $('#customer_id_verified').is(':checked'),
            signatureVerified: $('#signature_verified').is(':checked')
        };

        let previewHtml = '<div class="list-group list-group-flush">';
        
        if (preview.customer && preview.customer !== 'Select customer...') {
            previewHtml += `<div class="list-group-item"><strong>Customer:</strong> ${preview.customer}</div>`;
        }
        
        if (preview.amount && preview.currency && preview.currency !== 'Select currency...') {
            previewHtml += `<div class="list-group-item"><strong>Withdrawal:</strong> ${preview.amount} ${preview.currency.split(' - ')[0]}</div>`;
        }
        
        if (preview.commission) {
            previewHtml += `<div class="list-group-item"><strong>Fee:</strong> ${preview.commission}</div>`;
        }
        
        if (preview.method && preview.method !== 'Cash') {
            previewHtml += `<div class="list-group-item"><strong>Method:</strong> ${preview.method}</div>`;
        }
        
        const verificationStatus = [];
        if (preview.idVerified) verificationStatus.push('ID Verified');
        if (preview.signatureVerified) verificationStatus.push('Signature Verified');
        
        if (verificationStatus.length > 0) {
            previewHtml += `<div class="list-group-item"><strong>Verification:</strong> ${verificationStatus.join(', ')}</div>`;
        }
        
        previewHtml += '</div>';
        
        if (previewHtml === '<div class="list-group list-group-flush"></div>') {
            previewHtml = '<div class="text-muted text-center py-4"><i class="bi bi-info-circle"></i> Fill in the form to see transaction preview</div>';
        }
        
        $('#transaction-preview').html(previewHtml);
    }

    submitTransaction(action) {
        if (!this.validateWithdrawalForm()) {
            return;
        }

        // Set withdrawal-specific fields
        $('#to_currency').remove(); // Remove any existing hidden field
        $('<input>').attr({
            type: 'hidden',
            id: 'to_currency',
            name: 'to_currency',
            value: $('#from_currency').val()
        }).appendTo(this.form);

        $('<input>').attr({
            type: 'hidden',
            id: 'to_amount',
            name: 'to_amount',
            value: $('#from_amount').val()
        }).appendTo(this.form);

        $('<input>').attr({
            type: 'hidden',
            id: 'exchange_rate',
            name: 'exchange_rate',
            value: '1'
        }).appendTo(this.form);

        $('<input>').attr({
            type: 'hidden',
            id: 'delivery_method',
            name: 'delivery_method',
            value: 'cash'
        }).appendTo(this.form);

        TransactionUtils.submitTransaction(this.form, action)
            .then((response) => {
                const transactionId = response.id;
                const files = $('#document_files')[0].files;

                if (files.length > 0) {
                    TransactionUtils.uploadDocuments(transactionId, files)
                        .then(() => {
                            TransactionUtils.showAlert('success', 'Cash withdrawal and documents uploaded successfully');
                            setTimeout(() => {
                                window.location.href = '/transactions/type/WITHDRAWAL/';
                            }, 2000);
                        })
                        .catch((error) => {
                            TransactionUtils.showAlert('warning', `Cash withdrawal created but document upload failed: ${error.message}`);
                            setTimeout(() => {
                                window.location.href = '/transactions/type/WITHDRAWAL/';
                            }, 5000);
                        });
                } else {
                    TransactionUtils.showAlert('success', 'Cash withdrawal created successfully');
                    setTimeout(() => {
                        window.location.href = '/transactions/type/WITHDRAWAL/';
                    }, 2000);
                }
            })
            .catch((error) => {
                TransactionUtils.showAlert('danger', `Error creating cash withdrawal: ${error.message}`);
            });
    }

    // Removed - now using TransactionUtils.uploadDocuments()

    // Removed - now using TransactionUtils.handleFileSelection()

    getAuthHeaders() {
        return {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        };
    }

    // Removed - now using TransactionUtils.showAlert()
}

// Global function for commission calculation
function calculateWithdrawalCommission() {
    const amount = parseFloat($('#from_amount').val()) || 0;
    const currency = $('#from_currency').val();
    const location = $('#location').val();

    if (!amount || !currency) {
        TransactionUtils.showAlert('warning', 'Please enter amount and select currency first');
        return;
    }

    TransactionUtils.calculateCommission(amount, 'WITHDRAWAL', currency, currency, location)
        .then(result => {
            $('#commission_amount').val(result.commission_amount.toFixed(6));
            $('#commission_info').html(`
                <small class="text-muted">
                    Commission: ${result.commission_percentage}% = ${TransactionUtils.formatCurrency(result.commission_amount, currency)}
                </small>
            `);
        })
        .catch(error => {
            console.error('Error calculating commission:', error);
            TransactionUtils.showAlert('danger', 'Error calculating commission');
        });
}

$(document).ready(() => {
    new WithdrawalTransactionForm();
});

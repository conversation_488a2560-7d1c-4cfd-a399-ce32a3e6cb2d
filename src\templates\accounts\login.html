{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Login" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
/* Arena Doviz Login Page - Sharp/Rectangular Design */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000d28; /* Primary dark navy */
    background-image:
        linear-gradient(45deg, #000d28 25%, transparent 25%),
        linear-gradient(-45deg, #000d28 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #013121 75%),
        linear-gradient(-45deg, transparent 75%, #013121 75%);
    background-size: 60px 60px;
    background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
}

.login-card {
    background: white;
    border: 3px solid #000d28;
    box-shadow: 0 0 20px rgba(0, 13, 40, 0.3);
    padding: 3rem 2rem;
    width: 100%;
    max-width: 450px;
    /* NO border-radius for sharp design */
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
    border-bottom: 2px solid #000d28;
    padding-bottom: 1.5rem;
}

.login-header h2 {
    color: #000d28;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.login-header p {
    color: #6a0000; /* Secondary dark red */
    font-size: 0.9rem;
    font-weight: 500;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating .form-control {
    border: 2px solid #000d28;
    /* NO border-radius for sharp design */
    padding: 1rem 0.75rem;
    font-weight: 500;
}

.form-floating .form-control:focus {
    border-color: #013121; /* Tertiary dark green */
    box-shadow: 0 0 0 0.2rem rgba(1, 49, 33, 0.25);
}

.form-floating label {
    color: #000d28;
    font-weight: 600;
}

.btn-login {
    background-color: #000d28;
    border: 2px solid #000d28;
    color: white;
    /* NO border-radius for sharp design */
    padding: 15px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-login:hover {
    background-color: #6a0000; /* Secondary dark red */
    border-color: #6a0000;
    color: white;
    /* NO transform for sharp design */
}

.btn-login:active {
    background-color: #013121; /* Tertiary dark green */
    border-color: #013121;
}

.alert {
    /* NO border-radius for sharp design */
    border: 2px solid;
    font-weight: 600;
}

.alert-danger {
    border-color: #6a0000;
    background-color: rgba(106, 0, 0, 0.1);
    color: #6a0000;
}

.alert-success {
    border-color: #013121;
    background-color: rgba(1, 49, 33, 0.1);
    color: #013121;
}

.loading-spinner {
    display: none;
}

.form-check {
    margin: 1.5rem 0;
}

.form-check-input {
    border: 2px solid #000d28;
    /* NO border-radius for sharp design */
}

.form-check-input:checked {
    background-color: #000d28;
    border-color: #000d28;
}

.form-check-label {
    color: #000d28;
    font-weight: 500;
}

.forgot-password {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #000d28;
}

.forgot-password a {
    color: #000d28;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.forgot-password a:hover {
    color: #6a0000;
    text-decoration: underline;
}

/* Logo styling */
.login-logo {
    max-width: 120px;
    margin-bottom: 1rem;
}

/* Removed test credentials warning styles */
</style>
{% endblock %}

{% block navbar %}
<!-- Hide navbar on login page -->
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>{% trans "Welcome Back" %}</h2>
            <p>{% trans "Sign in to your Arena Doviz account" %}</p>
        </div>

        <div id="login-alerts"></div>

        <form id="login-form">
            {% csrf_token %}
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="{% trans 'Username' %}" required>
                <label for="username">{% trans "Username" %}</label>
            </div>

            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" placeholder="{% trans 'Password' %}" required>
                <label for="password">{% trans "Password" %}</label>
            </div>

            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                <label class="form-check-label" for="remember_me">
                    {% trans "Remember me" %}
                </label>
            </div>

            <button type="submit" class="btn btn-primary btn-login w-100">
                <span class="login-text">{% trans "Sign In" %}</span>
                <span class="loading-spinner">
                    <i class="bi bi-arrow-clockwise spin"></i>
                    {% trans "Signing in..." %}
                </span>
            </button>
        </form>

        <div class="forgot-password">
            <a href="#" onclick="showForgotPassword()">{% trans "Forgot your password?" %}</a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Check if user is already authenticated
    if (ArenaDoviz.auth.isAuthenticated()) {
        window.location.href = '/dashboard/';
        return;
    }

    // Handle login form submission
    $('#login-form').on('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });

    // Handle enter key in password field
    $('#password').on('keypress', function(e) {
        if (e.which === 13) {
            handleLogin();
        }
    });
});

function handleLogin() {
    const username = $('#username').val().trim();
    const password = $('#password').val();
    const rememberMe = $('#remember_me').is(':checked');

    // Validate inputs
    if (!username || !password) {
        showAlert('error', '{% trans "Please enter both username and password" %}');
        return;
    }

    // Show loading state
    setLoadingState(true);
    clearAlerts();

    // Attempt JWT login which will also create session authentication
    ArenaDoviz.auth.login(username, password)
        .then(data => {
            showAlert('success', '{% trans "Login successful! Redirecting..." %}');

            // Redirect to dashboard after short delay
            setTimeout(() => {
                window.location.href = '/dashboard/';
            }, 1000);
        })
        .catch(error => {
            console.error('Login error:', error);
            setLoadingState(false);

            // Handle specific error messages
            if (error.message.includes('401')) {
                showAlert('error', '{% trans "Invalid username or password" %}');
            } else if (error.message.includes('locked')) {
                showAlert('error', '{% trans "Account is temporarily locked due to failed login attempts" %}');
            } else {
                showAlert('error', '{% trans "Login failed. Please try again." %}');
            }
        });
}

function setLoadingState(loading) {
    const button = $('.btn-login');
    const loginText = $('.login-text');
    const loadingSpinner = $('.loading-spinner');
    
    if (loading) {
        button.prop('disabled', true);
        loginText.hide();
        loadingSpinner.show();
    } else {
        button.prop('disabled', false);
        loginText.show();
        loadingSpinner.hide();
    }
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
    const iconClass = type === 'error' ? 'bi-exclamation-triangle' : 'bi-check-circle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="bi ${iconClass} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('#login-alerts').html(alertHtml);
}

function clearAlerts() {
    $('#login-alerts').empty();
}

function showForgotPassword() {
    // TODO: Implement forgot password functionality
    showAlert('info', '{% trans "Forgot password functionality will be available soon. Please contact your administrator." %}');
}

// Add CSS animation for spinner
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}

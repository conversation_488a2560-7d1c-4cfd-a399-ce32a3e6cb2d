{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Login" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
/* Arena Doviz Login Page - Sharp/Rectangular Design */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000d28; /* Primary dark navy */
    background-image:
        linear-gradient(45deg, #000d28 25%, transparent 25%),
        linear-gradient(-45deg, #000d28 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #013121 75%),
        linear-gradient(-45deg, transparent 75%, #013121 75%);
    background-size: 60px 60px;
    background-position: 0 0, 0 30px, 30px -30px, -30px 0px;
}

.login-card {
    background: white;
    border: 3px solid #000d28;
    box-shadow: 0 0 20px rgba(0, 13, 40, 0.3);
    padding: 3rem 2rem;
    width: 100%;
    max-width: 450px;
    /* NO border-radius for sharp design */
}

.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
    border-bottom: 2px solid #000d28;
    padding-bottom: 1.5rem;
}

.login-header h1 {
    color: #000d28;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 28px;
}

.login-header p {
    color: #6a0000; /* Secondary dark red */
    font-size: 0.9rem;
    font-weight: 500;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #000d28;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 1rem 0.75rem;
    border: 2px solid #000d28;
    /* NO border-radius for sharp design */
    font-size: 14px;
    font-weight: 500;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #013121; /* Tertiary dark green */
    box-shadow: 0 0 0 0.2rem rgba(1, 49, 33, 0.25);
}

.btn-login {
    width: 100%;
    padding: 15px;
    background-color: #000d28;
    border: 2px solid #000d28;
    color: white;
    /* NO border-radius for sharp design */
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-login:hover {
    background-color: #6a0000; /* Secondary dark red */
    border-color: #6a0000;
    color: white;
    /* NO transform for sharp design */
}

.btn-login:active {
    background-color: #013121; /* Tertiary dark green */
    border-color: #013121;
}

.alert {
    padding: 12px 15px;
    /* NO border-radius for sharp design */
    border: 2px solid;
    font-weight: 600;
    margin-bottom: 20px;
}

.alert-danger {
    border-color: #6a0000;
    background-color: rgba(106, 0, 0, 0.1);
    color: #6a0000;
}

.alert-success {
    border-color: #013121;
    background-color: rgba(1, 49, 33, 0.1);
    color: #013121;
}

/* Test credentials styles removed for production */
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h1>{% trans "Arena Doviz" %}</h1>
            <p>{% trans "Exchange Accounting System" %}</p>
        </div>

        <!-- Test credentials removed for production -->

        <!-- Error Messages -->
        {% if form.errors %}
            <div class="alert alert-danger">
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        {{ error }}<br>
                    {% endfor %}
                {% endfor %}
            </div>
        {% endif %}

        <!-- Success Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}

        <!-- Login Form -->
        <form method="post" action="{% url 'accounts_web:login' %}">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">{% trans "Username" %}</label>
                {{ form.username }}
            </div>

            <div class="form-group">
                <label for="{{ form.password.id_for_label }}">{% trans "Password" %}</label>
                {{ form.password }}
            </div>

            <button type="submit" class="btn-login">
                {% trans "Login" %}
            </button>

            <!-- Hidden next field -->
            {% if next %}
                <input type="hidden" name="next" value="{{ next }}">
            {% endif %}
        </form>
    </div>
</div>

<script>
// Form initialization
document.addEventListener('DOMContentLoaded', function() {
    const usernameField = document.getElementById('{{ form.username.id_for_label }}');
    const passwordField = document.getElementById('{{ form.password.id_for_label }}');
    
    // Add proper form control classes
    if (usernameField) {
        usernameField.className = 'form-control';
        usernameField.placeholder = 'Enter your username';
    }
    if (passwordField) {
        passwordField.className = 'form-control';
        passwordField.placeholder = 'Enter your password';
    }
});
</script>
{% endblock %}

{% extends 'base.html' %}
{% load i18n static %}

{% block title %}{% trans "Transactions" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/transactions.css' %}">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-arrow-left-right"></i>
                {% trans "Transaction Management" %}
            </h1>
            <a href="{% url 'transactions_web:list' %}" class="btn btn-outline-secondary">
                <i class="bi bi-list"></i>
                {% trans "View All Transactions" %}
            </a>
        </div>
    </div>
</div>

<!-- Transaction Type Cards -->
<div class="row">
    <!-- Currency Exchange -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 transaction-type-card" data-type="exchange">
            <div class="card-header text-white" style="background-color: #000d28;">
                <h5 class="card-title mb-0">
                    <i class="bi bi-arrow-left-right"></i>
                    {% trans "Currency Exchange" %}
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">{% trans "Buy, sell, and exchange currencies with real-time rates and commission calculation." %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'transactions_web:type_list' 'EXCHANGE' %}" class="btn btn-outline-primary">
                        <i class="bi bi-list"></i>
                        {% trans "View Exchanges" %}
                    </a>
                    <a href="{% url 'transactions_web:type_add' 'EXCHANGE' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        {% trans "New Exchange" %}
                    </a>
                </div>
                <!-- Quick Access Sub-menu -->
                <div class="mt-3">
                    <div class="row g-1">
                        <div class="col-6">
                            <a href="{% url 'transactions_web:type_add' 'BUY' %}" class="btn btn-sm btn-outline-success w-100" title="{% trans 'Buy Currency' %}">
                                <i class="bi bi-arrow-down"></i>
                                <small>{% trans "Buy" %}</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{% url 'transactions_web:type_add' 'SELL' %}" class="btn btn-sm btn-outline-danger w-100" title="{% trans 'Sell Currency' %}">
                                <i class="bi bi-arrow-up"></i>
                                <small>{% trans "Sell" %}</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small id="exchange-count">{% trans "Loading..." %}</small>
            </div>
        </div>
    </div>

    <!-- Money Transfer -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 transaction-type-card" data-type="transfer">
            <div class="card-header text-white" style="background-color: #000d28;">
                <h5 class="card-title mb-0">
                    <i class="bi bi-send"></i>
                    {% trans "Money Transfer" %}
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">{% trans "Choose from internal, external, or international transfers with specialized forms for each type." %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'transactions_web:transfer_navigation' %}" class="btn btn-success">
                        <i class="bi bi-arrow-right"></i>
                        {% trans "Choose Transfer Type" %}
                    </a>
                </div>
                <!-- Quick Access Sub-menu -->
                <div class="mt-3">
                    <div class="row g-1">
                        <div class="col-4">
                            <a href="{% url 'transactions_web:internal_transfer_add' %}" class="btn btn-sm btn-outline-primary w-100" title="{% trans 'Internal Transfer' %}">
                                <i class="bi bi-arrow-left-right"></i>
                                <small>{% trans "Internal" %}</small>
                            </a>
                        </div>
                        <div class="col-4">
                            <a href="{% url 'transactions_web:external_transfer_add' %}" class="btn btn-sm btn-outline-success w-100" title="{% trans 'External Transfer' %}">
                                <i class="bi bi-bank"></i>
                                <small>{% trans "External" %}</small>
                            </a>
                        </div>
                        <div class="col-4">
                            <a href="{% url 'transactions_web:international_transfer_add' %}" class="btn btn-sm btn-outline-warning w-100" title="{% trans 'International Transfer' %}">
                                <i class="bi bi-globe"></i>
                                <small>{% trans "Intl" %}</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small id="transfer-count">{% trans "Loading..." %}</small>
            </div>
        </div>
    </div>

    <!-- Cash Transactions -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 transaction-type-card" data-type="cash">
            <div class="card-header text-white" style="background-color: #000d28;">
                <h5 class="card-title mb-0">
                    <i class="bi bi-cash"></i>
                    {% trans "Cash Transactions" %}
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">{% trans "Handle cash deposits and withdrawals with verification and courier integration." %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'transactions_web:cash_navigation' %}" class="btn btn-primary">
                        <i class="bi bi-arrow-right"></i>
                        {% trans "Choose Transaction Type" %}
                    </a>
                </div>
                <!-- Quick Access Sub-menu -->
                <div class="mt-3">
                    <div class="row g-1">
                        <div class="col-6">
                            <a href="{% url 'transactions_web:type_add' 'DEPOSIT' %}" class="btn btn-sm btn-outline-primary w-100" title="{% trans 'Cash Deposit' %}">
                                <i class="bi bi-cash-coin"></i>
                                <small>{% trans "Deposit" %}</small>
                            </a>
                        </div>
                        <div class="col-6">
                            <a href="{% url 'transactions_web:type_add' 'WITHDRAWAL' %}" class="btn btn-sm btn-outline-primary w-100" title="{% trans 'Cash Withdrawal' %}">
                                <i class="bi bi-cash-stack"></i>
                                <small>{% trans "Withdrawal" %}</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small id="cash-count">{% trans "Loading..." %}</small>
            </div>
        </div>
    </div>

    <!-- Remittance -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 transaction-type-card" data-type="remittance">
            <div class="card-header text-white" style="background-color: #000d28;">
                <h5 class="card-title mb-0">
                    <i class="bi bi-globe"></i>
                    {% trans "Remittance Service" %}
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">{% trans "International remittance services with beneficiary management and delivery tracking." %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'transactions_web:type_list' 'REMITTANCE' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-list"></i>
                        {% trans "View Remittances" %}
                    </a>
                    <a href="{% url 'transactions_web:type_add' 'REMITTANCE' %}" class="btn btn-secondary">
                        <i class="bi bi-plus-circle"></i>
                        {% trans "New Remittance" %}
                    </a>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small id="remittance-count">{% trans "Loading..." %}</small>
            </div>
        </div>
    </div>

    <!-- Balance Adjustment -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 transaction-type-card" data-type="adjustment">
            <div class="card-header text-white" style="background-color: #000d28;">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calculator"></i>
                    {% trans "Balance Adjustment" %}
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">{% trans "Manual balance adjustments for corrections, refunds, and administrative changes." %}</p>
                <div class="d-grid gap-2">
                    <a href="{% url 'transactions_web:type_list' 'ADJUSTMENT' %}" class="btn btn-outline-danger">
                        <i class="bi bi-list"></i>
                        {% trans "View Adjustments" %}
                    </a>
                    <a href="{% url 'transactions_web:type_add' 'ADJUSTMENT' %}" class="btn btn-danger">
                        <i class="bi bi-plus-circle"></i>
                        {% trans "New Adjustment" %}
                    </a>
                </div>
            </div>
            <div class="card-footer text-muted">
                <small id="adjustment-count">{% trans "Loading..." %}</small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {% trans "Today's Transaction Summary" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-primary" id="today-total">0</h4>
                            <small class="text-muted">{% trans "Total Today" %}</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-warning" id="pending-count">0</h4>
                            <small class="text-muted">{% trans "Pending" %}</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-success" id="completed-count">0</h4>
                            <small class="text-muted">{% trans "Completed" %}</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-info" id="total-volume">$0</h4>
                            <small class="text-muted">{% trans "Total Volume" %}</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="border-end">
                            <h4 class="text-secondary" id="commission-earned">$0</h4>
                            <small class="text-muted">{% trans "Commission" %}</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <h4 class="text-primary" id="active-customers">0</h4>
                        <small class="text-muted">{% trans "Active Customers" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% url 'javascript-catalog' %}"></script>
<script>
// Authentication headers
function getAuthHeaders() {
    return {
        'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
        'Content-Type': 'application/json'
    };
}

// Load transaction counts for each type
function loadTransactionCounts() {
    const transactionTypes = ['EXCHANGE', 'TRANSFER', 'DEPOSIT', 'WITHDRAWAL', 'REMITTANCE', 'ADJUSTMENT'];
    
    transactionTypes.forEach(function(typeCode) {
        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'GET',
            headers: getAuthHeaders(),
            data: {
                transaction_type_code: typeCode,
                page_size: 1
            },
            success: function(data) {
                const elementId = typeCode.toLowerCase() + '-count';
                const count = data.count || 0;
                const text = count === 1 ? 
                    `${count} {% trans "transaction" %}` : 
                    `${count} {% trans "transactions" %}`;
                $(`#${elementId}`).text(text);
            },
            error: function() {
                const elementId = typeCode.toLowerCase() + '-count';
                $(`#${elementId}`).text('{% trans "Error loading count" %}');
            }
        });
    });
}

// Load today's statistics
function loadTodayStats() {
    const today = new Date().toISOString().split('T')[0];
    
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        headers: getAuthHeaders(),
        data: {
            date_from: today,
            date_to: today,
            page_size: 1000 // Get all today's transactions for stats
        },
        success: function(data) {
            const transactions = data.results || [];
            
            // Calculate stats
            let totalVolume = 0;
            let totalCommission = 0;
            let pendingCount = 0;
            let completedCount = 0;
            const activeCustomers = new Set();
            
            transactions.forEach(function(transaction) {
                // Add to volume (convert to USD equivalent if needed)
                if (transaction.from_amount) {
                    totalVolume += parseFloat(transaction.from_amount);
                }
                
                // Add commission
                if (transaction.commission_amount) {
                    totalCommission += parseFloat(transaction.commission_amount);
                }
                
                // Count by status
                if (transaction.status === 'pending') {
                    pendingCount++;
                } else if (transaction.status === 'completed') {
                    completedCount++;
                }
                
                // Track active customers
                if (transaction.customer) {
                    activeCustomers.add(transaction.customer);
                }
            });
            
            // Update UI
            $('#today-total').text(transactions.length);
            $('#pending-count').text(pendingCount);
            $('#completed-count').text(completedCount);
            $('#total-volume').text('$' + totalVolume.toLocaleString());
            $('#commission-earned').text('$' + totalCommission.toLocaleString());
            $('#active-customers').text(activeCustomers.size);
        },
        error: function() {
            console.error('Error loading today\'s statistics');
        }
    });
}

// Add hover effects to transaction type cards
function initializeCardEffects() {
    $('.transaction-type-card').hover(
        function() {
            $(this).addClass('shadow-lg').css('transform', 'translateY(-5px)');
        },
        function() {
            $(this).removeClass('shadow-lg').css('transform', 'translateY(0)');
        }
    );
}

// Initialize page
$(document).ready(function() {
    loadTransactionCounts();
    loadTodayStats();
    initializeCardEffects();
    
    // Refresh stats every 5 minutes
    setInterval(function() {
        loadTransactionCounts();
        loadTodayStats();
    }, 300000);
});
</script>

<style>
.transaction-type-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.transaction-type-card:hover {
    transform: translateY(-5px);
}

.card-header {
    border-radius: 0 !important;
}

.border-end:last-child {
    border-right: none !important;
}
</style>

<script>
$(document).ready(function() {
    // Load transaction counts
    loadTransactionCounts();
});

function loadTransactionCounts() {
    const authHeaders = {
        'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
        'Content-Type': 'application/json'
    };

    // Load cash transactions count (deposits + withdrawals)
    Promise.all([
        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'GET',
            headers: authHeaders,
            data: { transaction_type__code: 'DEPOSIT', page_size: 1 }
        }),
        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'GET',
            headers: authHeaders,
            data: { transaction_type__code: 'WITHDRAWAL', page_size: 1 }
        })
    ]).then(function(results) {
        const depositCount = results[0].count || 0;
        const withdrawalCount = results[1].count || 0;
        const totalCash = depositCount + withdrawalCount;
        $('#cash-count').text(`${totalCash} transactions (${depositCount} deposits, ${withdrawalCount} withdrawals)`);
    }).catch(function() {
        $('#cash-count').text('Error loading count');
    });

    // Load other transaction type counts
    const transactionTypes = ['EXCHANGE', 'BUY', 'SELL', 'TRANSFER', 'EXTERNAL_TRANSFER', 'INTERNAL_TRANSFER', 'INTERNATIONAL_TRANSFER', 'REMITTANCE', 'ADJUSTMENT'];

    transactionTypes.forEach(function(type) {
        $.ajax({
            url: '/api/v1/transactions/transactions/',
            method: 'GET',
            headers: authHeaders,
            data: { transaction_type__code: type, page_size: 1 },
            success: function(data) {
                const count = data.count || 0;
                const elementId = type.toLowerCase() + '-count';
                $(`#${elementId}`).text(`${count} transactions`);
            },
            error: function() {
                const elementId = type.toLowerCase() + '-count';
                $(`#${elementId}`).text('Error loading count');
            }
        });
    });
}
</script>
{% endblock %}

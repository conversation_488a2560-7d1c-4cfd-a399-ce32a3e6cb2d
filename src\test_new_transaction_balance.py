#!/usr/bin/env python
"""
Test script to verify that new transactions create balance entries when approved.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

# Initialize Django
import django
django.setup()

from django.contrib.auth import get_user_model
from apps.customers.models import Customer
from apps.transactions.models import Transaction, TransactionType, BalanceEntry
from apps.currencies.models import Currency
from apps.locations.models import Location

User = get_user_model()

def test_deposit_transaction():
    """Test creating and approving a deposit transaction."""
    print("=== Testing Deposit Transaction Balance Creation ===\n")
    
    # Get required objects
    try:
        customer = Customer.objects.filter(status='active').first()
        location = Location.objects.filter(is_active=True).first()
        usd = Currency.objects.filter(code='USD').first()
        deposit_type = TransactionType.objects.filter(code='DEPOSIT').first()
        user = User.objects.filter(is_superuser=True).first()
        
        if not all([customer, location, usd, deposit_type, user]):
            print("❌ Missing required data for test")
            return False
            
        print(f"✅ Using customer: {customer.get_display_name()}")
        print(f"✅ Using location: {location.name}")
        print(f"✅ Using currency: {usd.code}")
        
        # Get initial balance
        initial_balance = customer.get_total_balance_in_currency('USD')
        print(f"📊 Initial customer balance: USD {initial_balance}")
        
        # Create deposit transaction
        print("\n📝 Creating deposit transaction...")
        transaction = Transaction.objects.create(
            transaction_type=deposit_type,
            customer=customer,
            location=location,
            from_currency=usd,
            to_currency=usd,
            from_amount=Decimal('500.00'),
            to_amount=Decimal('500.00'),
            exchange_rate=Decimal('1.0'),
            description='Test deposit for balance verification',
            status=Transaction.Status.PENDING,
            delivery_method='cash'
        )
        
        print(f"✅ Transaction created: {transaction.transaction_number}")
        print(f"   Status: {transaction.status}")
        print(f"   Balance entries before approval: {transaction.balance_entries.count()}")
        
        # Check balance before approval
        balance_before_approval = customer.get_total_balance_in_currency('USD')
        print(f"📊 Customer balance before approval: USD {balance_before_approval}")
        
        # Approve the transaction
        print("\n✅ Approving transaction...")
        transaction.status = Transaction.Status.APPROVED
        transaction.approved_by = user
        transaction.save()
        
        print(f"   New status: {transaction.status}")
        print(f"   Balance entries after approval: {transaction.balance_entries.count()}")
        
        # Check balance after approval
        balance_after_approval = customer.get_total_balance_in_currency('USD')
        print(f"📊 Customer balance after approval: USD {balance_after_approval}")
        
        # Verify balance entries were created
        if transaction.balance_entries.count() > 0:
            print("\n✅ Balance entries created:")
            for entry in transaction.balance_entries.all():
                customer_name = entry.customer.get_display_name() if entry.customer else 'Company'
                print(f"   {customer_name}: {entry.currency.code} {entry.amount} ({entry.entry_type})")
        else:
            print("\n❌ No balance entries created!")
            
        # Verify balance increased
        expected_balance = initial_balance + Decimal('500.00')
        if balance_after_approval == expected_balance:
            print(f"\n✅ Balance correctly updated! Expected: USD {expected_balance}, Actual: USD {balance_after_approval}")
            return True
        else:
            print(f"\n❌ Balance not updated correctly! Expected: USD {expected_balance}, Actual: USD {balance_after_approval}")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        return False

if __name__ == '__main__':
    success = test_deposit_transaction()
    if success:
        print("\n🎉 Test PASSED - Balance entries are created when transactions are approved!")
    else:
        print("\n💥 Test FAILED - Balance entries are not working correctly!")

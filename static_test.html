<!DOCTYPE html>
<html>
<head>
    <title>Arena Doviz Static Files Test</title>
    <link rel="stylesheet" href="/static/css/arena-doviz.css">
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
</head>
<body>
    <h1>Arena Doviz Static Files Test</h1>
    <div id="test-results"></div>
    
    <script src="/static/js/arena-doviz.js"></script>
    <script src="/static/js/charts.js"></script>
    
    <script>
    $(document).ready(function() {
        const results = $('#test-results');
        
        // Test ArenaDoviz object
        if (typeof ArenaDoviz !== 'undefined') {
            results.append('<p style="color: green;">✅ ArenaDoviz object loaded</p>');
            
            // Test auth module
            if (ArenaDoviz.auth) {
                results.append('<p style="color: green;">✅ ArenaDoviz.auth module loaded</p>');
            } else {
                results.append('<p style="color: red;">❌ ArenaDoviz.auth module missing</p>');
            }
            
            // Test utils module
            if (ArenaDoviz.utils) {
                results.append('<p style="color: green;">✅ ArenaDoviz.utils module loaded</p>');
            } else {
                results.append('<p style="color: red;">❌ ArenaDoviz.utils module missing</p>');
            }
            
        } else {
            results.append('<p style="color: red;">❌ ArenaDoviz object not defined</p>');
        }
        
        // Test jQuery
        if (typeof $ !== 'undefined') {
            results.append('<p style="color: green;">✅ jQuery loaded</p>');
        } else {
            results.append('<p style="color: red;">❌ jQuery not loaded</p>');
        }
    });
    </script>
</body>
</html>
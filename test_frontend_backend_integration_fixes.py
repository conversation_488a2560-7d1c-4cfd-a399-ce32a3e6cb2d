#!/usr/bin/env python3
"""
Arena Doviz Frontend-Backend Integration Fixes Test Script

This script tests all the fixes applied to resolve frontend-backend integration issues:
1. Authentication/Logout Problem
2. Missing getCurrentUser Function
3. Balance Validation Logic
4. Transaction List JavaScript Errors
5. Transaction Type Pages Null Reference Errors

Run this script to verify all fixes are working correctly.
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from decimal import Decimal
import json

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.development')
django.setup()

from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.transactions.models import Transaction, TransactionType
from apps.transactions.models import BalanceEntry

User = get_user_model()

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_test_result(test_name, success, message=""):
    """Print test result with emoji."""
    emoji = "✅" if success else "❌"
    print(f"{emoji} {test_name}: {'PASS' if success else 'FAIL'}")
    if message:
        print(f"   {message}")

def test_authentication_logout_fix():
    """Test Issue 0: Authentication/Logout Problem Fix"""
    print_header("Testing Authentication/Logout Fix")
    
    try:
        # Create test client
        client = Client()
        
        # Create test user
        user = User.objects.filter(username='test_logout_user').first()
        if not user:
            user = User.objects.create_user(
                username='test_logout_user',
                email='<EMAIL>',
                password='testpass123',
                first_name='Test',
                last_name='User'
            )
        
        # Test login
        login_success = client.login(username='test_logout_user', password='testpass123')
        print_test_result("User Login", login_success)
        
        if login_success:
            # Test accessing a protected page
            response = client.get('/dashboard/')
            print_test_result("Access Protected Page", response.status_code == 200)
            
            # Test logout endpoint exists
            response = client.post('/api/v1/accounts/users/logout/')
            print_test_result("Logout Endpoint Available", response.status_code in [200, 302])
            
            # Test JWT logout endpoint exists
            response = client.post('/api/v1/accounts/users/jwt_logout/')
            print_test_result("JWT Logout Endpoint Available", response.status_code in [200, 401])  # 401 is OK without JWT token
        
        return True
        
    except Exception as e:
        print_test_result("Authentication/Logout Test", False, str(e))
        return False

def test_balance_validation_fix():
    """Test Issue 1-2-4: Balance Validation Logic Fix"""
    print_header("Testing Balance Validation Fix")
    
    try:
        # Get or create test data
        customer = Customer.objects.filter(customer_code__startswith='TEST').first()
        if not customer:
            print_test_result("Balance Validation Test", False, "No test customer found")
            return False
        
        location = Location.objects.first()
        currency = Currency.objects.filter(code='AED').first()
        
        if not location or not currency:
            print_test_result("Balance Validation Test", False, "Missing test data (location/currency)")
            return False
        
        # Create a balance entry for the customer
        balance_entry = BalanceEntry.objects.filter(
            customer=customer,
            currency=currency,
            location=location
        ).first()
        
        if not balance_entry:
            balance_entry = BalanceEntry.objects.create(
                customer=customer,
                currency=currency,
                location=location,
                amount=Decimal('100.00'),
                running_balance=Decimal('100.00'),
                transaction_type='deposit',
                description='Test balance for validation'
            )
        
        print_test_result("Test Balance Entry Created", True, f"Balance: {balance_entry.running_balance}")
        
        # Test customer balance API endpoint
        client = Client()
        user = User.objects.first()
        client.force_login(user)
        
        response = client.get(f'/api/v1/customers/customers/{customer.id}/balance/')
        print_test_result("Customer Balance API", response.status_code == 200)
        
        if response.status_code == 200:
            balance_data = response.json()
            print_test_result("Balance Data Structure", isinstance(balance_data, list))
            
            if balance_data:
                first_balance = balance_data[0]
                has_currency_code = 'currency_code' in first_balance
                has_balance = 'balance' in first_balance
                print_test_result("Balance Data Fields", has_currency_code and has_balance)
        
        return True
        
    except Exception as e:
        print_test_result("Balance Validation Test", False, str(e))
        return False

def test_transaction_list_fix():
    """Test Issue 5: Transaction List JavaScript Errors Fix"""
    print_header("Testing Transaction List Fix")
    
    try:
        client = Client()
        user = User.objects.first()
        client.force_login(user)
        
        # Test transaction list page loads
        response = client.get('/transactions/list/')
        print_test_result("Transaction List Page", response.status_code == 200)
        
        # Check if the page contains the fixed JavaScript (no loadTransactions call)
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            has_datatable_reload = 'transactionsTable.ajax.reload()' in content
            has_old_loadtransactions = 'loadTransactions()' in content
            print_test_result("JavaScript Fix Applied", has_datatable_reload and not has_old_loadtransactions)
        
        # Test transaction approval API endpoint
        transaction = Transaction.objects.filter(status='draft').first()
        if transaction:
            response = client.post(f'/api/v1/transactions/transactions/{transaction.id}/approve/')
            # Should return 400 if transaction can't be approved, or 200 if successful
            print_test_result("Transaction Approval API", response.status_code in [200, 400, 403])
        else:
            print_test_result("Transaction Approval API", True, "No draft transactions to test")
        
        return True
        
    except Exception as e:
        print_test_result("Transaction List Test", False, str(e))
        return False

def test_null_reference_fix():
    """Test Issue 6: Transaction Type Pages Null Reference Errors Fix"""
    print_header("Testing Null Reference Errors Fix")
    
    try:
        client = Client()
        user = User.objects.first()
        client.force_login(user)
        
        # Test various transaction type pages
        transaction_pages = [
            '/transactions/exchange/add/',
            '/transactions/transfer/internal/add/',
            '/transactions/transfer/external/add/',
            '/transactions/transfer/international/add/',
            '/transactions/deposit/add/',
            '/transactions/withdrawal/add/',
        ]
        
        all_pages_work = True
        for page_url in transaction_pages:
            try:
                response = client.get(page_url)
                page_works = response.status_code == 200
                page_name = page_url.split('/')[-3] if len(page_url.split('/')) > 3 else page_url
                print_test_result(f"{page_name.title()} Page", page_works)
                
                if page_works:
                    # Check if the page contains the defensive programming fix
                    content = response.content.decode('utf-8')
                    has_form_check = 'if (this.form)' in content or 'transaction-form' in content
                    print_test_result(f"{page_name.title()} Form Check", has_form_check)
                
                all_pages_work = all_pages_work and page_works
                
            except Exception as e:
                print_test_result(f"{page_name.title()} Page", False, str(e))
                all_pages_work = False
        
        return all_pages_work
        
    except Exception as e:
        print_test_result("Null Reference Test", False, str(e))
        return False

def test_javascript_functions():
    """Test that JavaScript functions are properly defined"""
    print_header("Testing JavaScript Functions")
    
    try:
        client = Client()
        user = User.objects.first()
        client.force_login(user)
        
        # Test that arena-doviz.js loads properly
        response = client.get('/static/js/arena-doviz.js')
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Check for getCurrentUser function
            has_get_current_user = 'getCurrentUser: function()' in content
            print_test_result("getCurrentUser Function", has_get_current_user)
            
            # Check for logout function improvements
            has_logout_improvements = 'arena_logout_in_progress' in content
            print_test_result("Logout Function Improvements", has_logout_improvements)
            
            # Check for authentication check improvements
            has_auth_improvements = 'logoutInProgress' in content
            print_test_result("Authentication Check Improvements", has_auth_improvements)
            
            return has_get_current_user and has_logout_improvements
        else:
            print_test_result("JavaScript File Access", False, f"Status: {response.status_code}")
            return False
        
    except Exception as e:
        print_test_result("JavaScript Functions Test", False, str(e))
        return False

def main():
    """Run all tests"""
    print_header("Arena Doviz Frontend-Backend Integration Fixes Test")
    print("Testing all fixes applied to resolve integration issues...")
    
    test_results = []
    
    # Run all tests
    test_results.append(("Authentication/Logout Fix", test_authentication_logout_fix()))
    test_results.append(("Balance Validation Fix", test_balance_validation_fix()))
    test_results.append(("Transaction List Fix", test_transaction_list_fix()))
    test_results.append(("Null Reference Fix", test_null_reference_fix()))
    test_results.append(("JavaScript Functions", test_javascript_functions()))
    
    # Summary
    print_header("Test Summary")
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    for test_name, result in test_results:
        print_test_result(test_name, result)
    
    print(f"\n📊 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes are working correctly!")
        return True
    else:
        print("⚠️  Some issues may still need attention.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

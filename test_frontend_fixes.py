#!/usr/bin/env python3
"""
Test Frontend Fixes for Arena Doviz Production Issues

This script tests the fixes for:
1. Static file serving (404 errors)
2. Courier UUID validation errors
3. JavaScript loading and ArenaDoviz object availability
4. MIME type issues

Usage:
    python test_frontend_fixes.py
"""

import os
import sys
import django
import requests
import json
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.conf import settings
from apps.transactions.models import Transaction
from apps.customers.models import Customer
from apps.currencies.models import Currency
from apps.locations.models import Location

User = get_user_model()

def test_static_files():
    """Test static file serving."""
    print("\n🔧 Testing Static File Serving")
    print("-" * 40)
    
    client = Client()
    
    # Test critical static files
    static_files = [
        '/static/js/arena-doviz.js',
        '/static/js/charts.js',
        '/static/css/arena-doviz.css',
        '/static/css/transactions.css'
    ]
    
    results = []
    for file_path in static_files:
        try:
            response = client.get(file_path)
            status = response.status_code
            content_type = response.get('Content-Type', 'unknown')
            
            if status == 200:
                print(f"✅ {file_path} - {status} - {content_type}")
                results.append(True)
            else:
                print(f"❌ {file_path} - {status}")
                results.append(False)
        except Exception as e:
            print(f"❌ {file_path} - Error: {e}")
            results.append(False)
    
    return all(results)

def test_javascript_content():
    """Test JavaScript file content."""
    print("\n🔍 Testing JavaScript Content")
    print("-" * 40)
    
    try:
        # Check arena-doviz.js content
        js_file = Path(settings.STATIC_ROOT) / 'js' / 'arena-doviz.js'
        
        if not js_file.exists():
            print("❌ arena-doviz.js not found")
            return False
        
        content = js_file.read_text(encoding='utf-8')
        
        # Check for key components
        checks = [
            ('ArenaDoviz object', 'const ArenaDoviz' in content or 'var ArenaDoviz' in content),
            ('Auth module', 'auth:' in content),
            ('Utils module', 'utils:' in content),
            ('API functions', 'fetch(' in content or '$.ajax' in content),
            ('Courier processing', 'processCourierField' in content),
        ]
        
        all_good = True
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        print(f"\nFile size: {len(content)} characters")
        return all_good
        
    except Exception as e:
        print(f"❌ Error checking JavaScript: {e}")
        return False

def test_courier_field_processing():
    """Test courier field processing logic."""
    print("\n👤 Testing Courier Field Processing")
    print("-" * 40)
    
    try:
        # Check if the courier processing logic exists in JavaScript
        js_file = Path(settings.STATIC_ROOT) / 'js' / 'transactions' / 'common.js'
        
        if not js_file.exists():
            print("❌ common.js not found")
            return False
        
        content = js_file.read_text(encoding='utf-8')
        
        # Check for courier processing improvements
        checks = [
            ('processCourierField method', 'processCourierField' in content),
            ('UUID validation', 'uuidRegex' in content),
            ('Customer courier handling', 'courier_type' in content),
            ('Data attribute handling', 'data-courier-type' in content),
        ]
        
        all_good = True
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error checking courier processing: {e}")
        return False

def test_transaction_serializer():
    """Test transaction serializer courier field handling."""
    print("\n📝 Testing Transaction Serializer")
    print("-" * 40)
    
    try:
        from apps.transactions.serializers import TransactionCreateSerializer
        
        # Check if serializer has courier_name and courier_type fields
        serializer = TransactionCreateSerializer()
        fields = serializer.fields.keys()
        
        checks = [
            ('courier_name field', 'courier_name' in fields),
            ('courier_type field', 'courier_type' in fields),
            ('courier field', 'courier' in fields),
        ]
        
        all_good = True
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error checking serializer: {e}")
        return False

def test_whitenoise_configuration():
    """Test WhiteNoise configuration."""
    print("\n🌐 Testing WhiteNoise Configuration")
    print("-" * 40)
    
    try:
        # Check if WhiteNoise is in middleware
        middleware = settings.MIDDLEWARE
        whitenoise_middleware = 'whitenoise.middleware.WhiteNoiseMiddleware'
        
        checks = [
            ('WhiteNoise middleware', whitenoise_middleware in middleware),
            ('Static files storage', 'whitenoise' in settings.STATICFILES_STORAGE.lower()),
            ('Static root exists', Path(settings.STATIC_ROOT).exists()),
            ('Static URL configured', settings.STATIC_URL == '/static/'),
        ]
        
        all_good = True
        for check_name, check_result in checks:
            if check_result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error checking WhiteNoise: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Arena Doviz Frontend Fixes Test")
    print("=" * 50)
    
    tests = [
        ("Static File Serving", test_static_files),
        ("JavaScript Content", test_javascript_content),
        ("Courier Field Processing", test_courier_field_processing),
        ("Transaction Serializer", test_transaction_serializer),
        ("WhiteNoise Configuration", test_whitenoise_configuration),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Frontend fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

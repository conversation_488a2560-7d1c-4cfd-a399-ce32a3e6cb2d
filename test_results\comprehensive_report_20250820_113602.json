{"start_time": "2025-08-20T11:35:42.018965", "browser": "chromium", "headless": true, "tests": {"Authentication Tests": {"status": "failed", "output": "", "errors": "INFO Arena Doviz Accounts app is ready\nDEBUG Accounts app signals imported successfully\nDEBUG Initializing authentication services...\nDEBUG Default group already exists: Admin\nDEBUG Default group already exists: Accountant\nDEBUG Default group already exists: Branch Employee\nDEBUG Default group already exists: Viewer\nDEBUG Default group already exists: Courier\nINFO Authentication services initialized successfully\nINFO Arena Doviz Customers app is ready\nDEBUG Customers app signals imported successfully\nDEBUG Initializing customer services...\nDEBUG Customer categories setup completed\nINFO Customer services initialized successfully\nINFO Arena Doviz Locations app is ready\nDEBUG Locations app signals imported successfully\nDEBUG Initializing default locations...\nDEBUG Default location already exists: Istanbul\nDEBUG Default location already exists: Tabriz\nDEBUG Default location already exists: Tehran\nDEBUG Default location already exists: Dubai\nDEBUG Default location already exists: China\nINFO Arena Doviz Currencies app is ready\nDEBUG Currencies app signals imported successfully\nDEBUG Initializing default currencies...\nDEBUG Default currency already exists: USD\nDEBUG Default currency already exists: AED\nDEBUG Default currency already exists: IRR\nINFO Arena Doviz Transactions app is ready\nDEBUG Transactions app signals imported successfully\nDEBUG Initializing transaction services...\nDEBUG Transaction types setup completed\nDEBUG Commission rules already exist, skipping default setup\nINFO Transaction services initialized successfully\nINFO Arena Doviz Reports app is ready\nDEBUG Reports app signals imported successfully\nDEBUG Initializing reporting services...\nDEBUG Report templates setup completed\nINFO Reporting services initialized successfully\nINFO Arena Doviz Core app is ready\nDEBUG Core app signals imported successfully\nDEBUG Initializing core services...\nINFO Application logging configured\nDEBUG Cache connection verified\nINFO Core services initialized successfully\nERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results/pytest_authentication_tests.json --headless\n  inifile: None\n  rootdir: C:\\Users\\<USER>\\Documents\\exchange-accounting\n\n"}, "Comprehensive System Tests": {"status": "failed", "output": "", "errors": "INFO Arena Doviz Accounts app is ready\nDEBUG Accounts app signals imported successfully\nDEBUG Initializing authentication services...\nDEBUG Default group already exists: Admin\nDEBUG Default group already exists: Accountant\nDEBUG Default group already exists: Branch Employee\nDEBUG Default group already exists: Viewer\nDEBUG Default group already exists: Courier\nINFO Authentication services initialized successfully\nINFO Arena Doviz Customers app is ready\nDEBUG Customers app signals imported successfully\nDEBUG Initializing customer services...\nDEBUG Customer categories setup completed\nINFO Customer services initialized successfully\nINFO Arena Doviz Locations app is ready\nDEBUG Locations app signals imported successfully\nDEBUG Initializing default locations...\nDEBUG Default location already exists: Istanbul\nDEBUG Default location already exists: Tabriz\nDEBUG Default location already exists: Tehran\nDEBUG Default location already exists: Dubai\nDEBUG Default location already exists: China\nINFO Arena Doviz Currencies app is ready\nDEBUG Currencies app signals imported successfully\nDEBUG Initializing default currencies...\nDEBUG Default currency already exists: USD\nDEBUG Default currency already exists: AED\nDEBUG Default currency already exists: IRR\nINFO Arena Doviz Transactions app is ready\nDEBUG Transactions app signals imported successfully\nDEBUG Initializing transaction services...\nDEBUG Transaction types setup completed\nDEBUG Commission rules already exist, skipping default setup\nINFO Transaction services initialized successfully\nINFO Arena Doviz Reports app is ready\nDEBUG Reports app signals imported successfully\nDEBUG Initializing reporting services...\nDEBUG Report templates setup completed\nINFO Reporting services initialized successfully\nINFO Arena Doviz Core app is ready\nDEBUG Core app signals imported successfully\nDEBUG Initializing core services...\nINFO Application logging configured\nDEBUG Cache connection verified\nINFO Core services initialized successfully\nERROR: usage: __main__.py [options] [file_or_dir] [file_or_dir] [...]\n__main__.py: error: unrecognized arguments: --json-report --json-report-file=test_results/pytest_comprehensive_system_tests.json --headless\n  inifile: None\n  rootdir: C:\\Users\\<USER>\\Documents\\exchange-accounting\n\n"}}, "errors": ["Test data creation failed: Invalid field name(s) for model Customer: 'id_number', 'id_type', 'is_active'."], "performance": {"page_load_times": {}, "memory_usage": {}, "network_requests": {}}, "summary": {"total_categories": 2, "passed_categories": 0, "failed_categories": 2, "success_rate": 0.0}, "end_time": "2025-08-20T11:36:02.238753", "duration": "0:00:20.219813"}
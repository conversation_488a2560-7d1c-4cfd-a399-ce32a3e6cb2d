#!/usr/bin/env python3
"""
Test URL Routing for Arena Doviz

This script tests all the URL routes to ensure they work correctly.
"""

import os
import sys
import django
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.prod')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.test import Client
from django.urls import reverse
from django.contrib.auth import get_user_model

User = get_user_model()

def test_url_routes():
    """Test various URL routes."""
    print("🔗 Testing URL Routes")
    print("=" * 40)
    
    client = Client()
    
    # Test URLs to check
    test_urls = [
        ('/', 'Root URL'),
        ('/login/', 'Login redirect'),
        ('/accounts/login/', 'Accounts login'),
        ('/signin/', 'Signin redirect'),
        ('/auth/login/', 'Auth login redirect'),
        ('/dashboard/', 'Dashboard'),
        ('/admin/', 'Admin'),
        ('/api/v1/accounts/jwt/token/', 'JWT Token API'),
    ]
    
    results = []
    
    for url, description in test_urls:
        try:
            response = client.get(url)
            status = response.status_code
            
            # Determine if this is a success
            success = status in [200, 301, 302]  # OK, Moved Permanently, Found
            
            if success:
                if status == 301:
                    redirect_url = response.get('Location', 'Unknown')
                    print(f"✅ {description}: {status} → {redirect_url}")
                elif status == 302:
                    redirect_url = response.get('Location', 'Unknown')
                    print(f"✅ {description}: {status} → {redirect_url}")
                else:
                    print(f"✅ {description}: {status}")
            else:
                print(f"❌ {description}: {status}")
            
            results.append((description, success, status))
            
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            results.append((description, False, 'Error'))
    
    return results

def test_login_functionality():
    """Test login functionality."""
    print("\n🔐 Testing Login Functionality")
    print("=" * 40)
    
    client = Client()
    
    # Test login page access
    response = client.get('/accounts/login/')
    if response.status_code == 200:
        print("✅ Login page accessible")
        
        # Check if it contains login form
        content = response.content.decode()
        if 'username' in content and 'password' in content:
            print("✅ Login form found on page")
        else:
            print("❌ Login form not found on page")
            
        return True
    else:
        print(f"❌ Login page not accessible: {response.status_code}")
        return False

def test_api_endpoints():
    """Test API endpoints."""
    print("\n🔌 Testing API Endpoints")
    print("=" * 40)
    
    client = Client()
    
    api_endpoints = [
        ('/api/v1/accounts/jwt/token/', 'JWT Token'),
        ('/api/v1/core/dashboard/', 'Dashboard API'),
        ('/api/v1/customers/customers/', 'Customers API'),
        ('/api/v1/transactions/transactions/', 'Transactions API'),
    ]
    
    results = []
    
    for endpoint, description in api_endpoints:
        try:
            response = client.get(endpoint)
            status = response.status_code
            
            # For API endpoints, we expect 401 (unauthorized) or 405 (method not allowed) for GET requests
            # or 200 for endpoints that allow anonymous access
            success = status in [200, 401, 405]
            
            if success:
                print(f"✅ {description}: {status}")
            else:
                print(f"❌ {description}: {status}")
            
            results.append((description, success, status))
            
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            results.append((description, False, 'Error'))
    
    return results

def display_access_info():
    """Display access information."""
    print("\n🌐 ACCESS INFORMATION")
    print("=" * 40)
    
    import socket
    hostname = socket.gethostname()
    local_ip = socket.gethostbyname(hostname)
    
    print(f"Server IP: {local_ip}")
    print(f"Server Port: 8000")
    print()
    print("📱 ACCESS URLS:")
    print(f"   Local: http://localhost:8000")
    print(f"   Network: http://{local_ip}:8000")
    print(f"   External: http://*************:8000")
    print()
    print("🔗 LOGIN URLS (all redirect to /accounts/login/):")
    print(f"   http://{local_ip}:8000/login/")
    print(f"   http://{local_ip}:8000/signin/")
    print(f"   http://{local_ip}:8000/accounts/login/")
    print()
    print("🔐 LOGIN CREDENTIALS:")
    print("   Username: admin_user")
    print("   Password: Admin123!@#")

def main():
    """Main function."""
    print("🚀 Arena Doviz URL Routing Test")
    print("=" * 50)
    
    # Test URL routes
    url_results = test_url_routes()
    
    # Test login functionality
    login_success = test_login_functionality()
    
    # Test API endpoints
    api_results = test_api_endpoints()
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 40)
    
    url_success = sum(1 for _, success, _ in url_results if success)
    api_success = sum(1 for _, success, _ in api_results if success)
    
    print(f"URL Routes: {url_success}/{len(url_results)} working")
    print(f"Login Page: {'✅' if login_success else '❌'}")
    print(f"API Endpoints: {api_success}/{len(api_results)} responding")
    
    # Display access information
    display_access_info()
    
    total_tests = len(url_results) + len(api_results) + 1  # +1 for login test
    total_success = url_success + api_success + (1 if login_success else 0)
    
    if total_success == total_tests:
        print("\n🎉 All URL routes are working correctly!")
        print("\n✅ READY FOR PRODUCTION:")
        print("1. Start server: python manage.py runserver 0.0.0.0:8000")
        print("2. Access from any device using the URLs above")
        print("3. Login redirects are working for /login/, /signin/, etc.")
        
        return True
    else:
        print(f"\n⚠️  Some issues found: {total_success}/{total_tests} tests passed")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

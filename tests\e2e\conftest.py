"""
Pytest configuration for Arena Doviz E2E tests
"""

import pytest
import os
import sys
import django
import asyncio
import subprocess
import time
from pathlib import Path
from playwright.async_api import async_playwright

# Add the src directory to Python path
src_path = Path(__file__).parent.parent.parent / 'src'
sys.path.insert(0, str(src_path))

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')

# Set encryption key if not set
if not os.environ.get('ARENA_ENCRYPTION_KEY'):
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from apps.customers.models import Customer
from apps.currencies.models import Currency
from apps.locations.models import Location
from apps.transactions.models import TransactionType

User = get_user_model()

@pytest.fixture(scope="session")
def django_db_setup():
    """Set up test database."""
    pass

@pytest.fixture(scope="session")
def server_url():
    """Base URL for the test server."""
    return "http://localhost:8000"

@pytest.fixture(scope="session")
async def browser():
    """Create browser instance for tests."""
    async with async_playwright() as p:
        browser = await p.chromium.launch(
            headless=False,  # Set to True for CI/CD
            args=['--disable-web-security', '--disable-features=VizDisplayCompositor']
        )
        yield browser
        await browser.close()

@pytest.fixture
async def page(browser):
    """Create a new page for each test."""
    context = await browser.new_context(
        viewport={'width': 1920, 'height': 1080},
        ignore_https_errors=True
    )
    page = await context.new_page()
    
    # Enable console logging
    page.on("console", lambda msg: print(f"Console {msg.type}: {msg.text}"))
    
    # Enable error logging
    page.on("pageerror", lambda error: print(f"Page error: {error}"))
    
    yield page
    await context.close()

@pytest.fixture(scope="session")
def test_user():
    """Create or get test user."""
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'is_active': True,
            'is_staff': True,
        }
    )
    if created:
        user.set_password('testpass123')
        user.save()
    return user

@pytest.fixture(scope="session")
def test_customer():
    """Create or get test customer."""
    customer, created = Customer.objects.get_or_create(
        phone_number='+1234567890',
        defaults={
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'customer_type': Customer.CustomerType.INDIVIDUAL,
            'status': Customer.Status.ACTIVE,
        }
    )
    return customer

@pytest.fixture(scope="session")
def test_currencies():
    """Ensure test currencies exist."""
    currencies = []
    currency_data = [
        {'code': 'USD', 'name': 'US Dollar', 'symbol': '$'},
        {'code': 'AED', 'name': 'UAE Dirham', 'symbol': 'د.إ'},
        {'code': 'IRR', 'name': 'Iranian Rial', 'symbol': '﷼'},
    ]
    
    for data in currency_data:
        currency, created = Currency.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        currencies.append(currency)
    
    return currencies

@pytest.fixture(scope="session")
def test_locations():
    """Ensure test locations exist."""
    locations = []
    location_data = [
        {'name': 'Istanbul', 'code': 'IST', 'country': 'Turkey'},
        {'name': 'Dubai', 'code': 'DXB', 'country': 'UAE'},
        {'name': 'Tehran', 'code': 'TEH', 'country': 'Iran'},
    ]
    
    for data in location_data:
        location, created = Location.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        locations.append(location)
    
    return locations

@pytest.fixture(scope="session")
def test_transaction_types():
    """Ensure test transaction types exist."""
    transaction_types = []
    type_data = [
        {'code': 'EXCHANGE', 'name': 'Currency Exchange', 'is_exchange': True},
        {'code': 'TRANSFER', 'name': 'Money Transfer', 'is_exchange': False},
        {'code': 'DEPOSIT', 'name': 'Cash Deposit', 'is_exchange': False},
        {'code': 'WITHDRAWAL', 'name': 'Cash Withdrawal', 'is_exchange': False},
        {'code': 'REMITTANCE', 'name': 'Remittance', 'is_exchange': False},
        {'code': 'ADJUSTMENT', 'name': 'Balance Adjustment', 'is_exchange': False},
    ]
    
    for data in type_data:
        transaction_type, created = TransactionType.objects.get_or_create(
            code=data['code'],
            defaults=data
        )
        transaction_types.append(transaction_type)
    
    return transaction_types

@pytest.fixture
async def authenticated_page(page, server_url, test_user):
    """Create an authenticated page session."""
    # Navigate to login page
    await page.goto(f"{server_url}/accounts/login/")
    
    # Wait for page to load
    await page.wait_for_selector('#id_username')
    
    # Fill login form
    await page.fill('#id_username', test_user.username)
    await page.fill('#id_password', 'testpass123')
    
    # Submit form
    await page.click('button[type="submit"]')
    
    # Wait for redirect to dashboard
    await page.wait_for_url(f"{server_url}/dashboard/")
    
    return page

@pytest.fixture
def error_collector():
    """Collect JavaScript errors during test execution."""
    errors = []
    
    def collect_error(error):
        errors.append({
            'message': str(error),
            'timestamp': time.time()
        })
    
    return {
        'errors': errors,
        'collect': collect_error,
        'get_errors': lambda: errors.copy(),
        'clear': lambda: errors.clear()
    }

@pytest.fixture
async def console_monitor(page):
    """Monitor console messages during test execution."""
    console_messages = []
    
    def handle_console(msg):
        console_messages.append({
            'type': msg.type,
            'text': msg.text,
            'timestamp': time.time()
        })
    
    page.on("console", handle_console)
    
    yield {
        'messages': console_messages,
        'get_errors': lambda: [msg for msg in console_messages if msg['type'] == 'error'],
        'get_warnings': lambda: [msg for msg in console_messages if msg['type'] == 'warning'],
        'clear': lambda: console_messages.clear()
    }

@pytest.fixture
async def network_monitor(page):
    """Monitor network requests during test execution."""
    requests = []
    responses = []
    
    def handle_request(request):
        requests.append({
            'url': request.url,
            'method': request.method,
            'timestamp': time.time()
        })
    
    def handle_response(response):
        responses.append({
            'url': response.url,
            'status': response.status,
            'timestamp': time.time()
        })
    
    page.on("request", handle_request)
    page.on("response", handle_response)
    
    yield {
        'requests': requests,
        'responses': responses,
        'get_failed_requests': lambda: [r for r in responses if r['status'] >= 400],
        'clear': lambda: (requests.clear(), responses.clear())
    }

class TestDataManager:
    """Manage test data creation and cleanup."""
    
    def __init__(self):
        self.created_objects = []
    
    def create_customer(self, **kwargs):
        """Create a test customer."""
        defaults = {
            'first_name': 'Test',
            'last_name': 'Customer',
            'phone_number': f'+123456{len(self.created_objects)}',
            'email': f'test{len(self.created_objects)}@example.com',
            'customer_type': Customer.CustomerType.INDIVIDUAL,
            'status': Customer.Status.ACTIVE,
        }
        defaults.update(kwargs)

        customer = Customer.objects.create(**defaults)
        self.created_objects.append(customer)
        return customer
    
    def cleanup(self):
        """Clean up created test objects."""
        for obj in reversed(self.created_objects):
            try:
                obj.delete()
            except Exception as e:
                print(f"Error deleting {obj}: {e}")
        self.created_objects.clear()

@pytest.fixture
def test_data_manager():
    """Provide test data manager."""
    manager = TestDataManager()
    yield manager
    manager.cleanup()

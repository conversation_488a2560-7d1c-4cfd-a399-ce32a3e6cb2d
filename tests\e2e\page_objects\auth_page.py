"""
Authentication page objects for Arena Doviz E2E tests
"""

from playwright.async_api import Page, expect
from .base_page import BasePage


class LoginPage(BasePage):
    """Login page object."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.url = "/accounts/login/"
        
        # Selectors
        self.username_input = "#id_username"
        self.password_input = "#id_password"
        self.login_button = 'button[type="submit"]'
        self.error_message = ".alert-danger"
        self.form = "form"
    
    async def navigate(self):
        """Navigate to login page."""
        await self.goto(self.url)
        await self.verify_page_loaded()
    
    async def login(self, username: str, password: str):
        """Perform login with credentials."""
        await self.fill_form_field(self.username_input, username)
        await self.fill_form_field(self.password_input, password)
        await self.click_and_wait(self.login_button)
        await self.wait_for_form_submission()
    
    async def login_with_valid_credentials(self, username: str = "testuser", password: str = "testpass123"):
        """Login with valid test credentials."""
        await self.navigate()
        await self.login(username, password)
        
        # Verify successful login (should redirect to dashboard)
        await self.page.wait_for_url(f"{self.base_url}/dashboard/")
        await self.verify_no_console_errors()
    
    async def login_with_invalid_credentials(self, username: str = "invalid", password: str = "invalid"):
        """Login with invalid credentials."""
        await self.navigate()
        await self.login(username, password)
        
        # Should stay on login page with error message
        await self.wait_for_element(self.error_message)
        error_text = await self.get_text(self.error_message)
        assert "invalid" in error_text.lower() or "incorrect" in error_text.lower()
    
    async def verify_login_form_validation(self):
        """Test login form validation."""
        await self.navigate()
        
        # Try to submit empty form
        await self.click_and_wait(self.login_button)
        
        # Check for HTML5 validation or custom validation
        username_field = self.page.locator(self.username_input)
        password_field = self.page.locator(self.password_input)
        
        # Check if fields are marked as required
        await expect(username_field).to_have_attribute("required", "")
        await expect(password_field).to_have_attribute("required", "")
    
    async def verify_page_elements(self):
        """Verify all expected page elements are present."""
        await self.navigate()
        
        # Check form elements
        await expect(self.page.locator(self.username_input)).to_be_visible()
        await expect(self.page.locator(self.password_input)).to_be_visible()
        await expect(self.page.locator(self.login_button)).to_be_visible()
        
        # Check page title
        title = await self.get_page_title()
        assert "login" in title.lower() or "arena doviz" in title.lower()
        
        # Verify static files loaded
        await self.verify_static_files_loaded()


class LogoutPage(BasePage):
    """Logout functionality."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.logout_button = 'a[href*="logout"], button:has-text("Logout")'
        self.user_menu = ".dropdown-toggle, .user-menu"
    
    async def logout(self):
        """Perform logout."""
        # Look for logout button in navigation
        if await self.is_visible(self.user_menu):
            await self.page.click(self.user_menu)
            await asyncio.sleep(0.5)  # Wait for dropdown
        
        await self.click_and_wait(self.logout_button)
        
        # Should redirect to login page
        await self.page.wait_for_url(f"{self.base_url}/accounts/login/")
        await self.verify_no_console_errors()
    
    async def verify_logout_redirects_to_login(self):
        """Verify that logout properly redirects to login page."""
        await self.logout()
        
        # Verify we're on login page
        current_url = self.page.url
        assert "/accounts/login/" in current_url
        
        # Verify login form is visible
        await expect(self.page.locator("#id_username")).to_be_visible()


class AuthenticationFlow(BasePage):
    """Complete authentication flow testing."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.login_page = LoginPage(page, base_url)
        self.logout_page = LogoutPage(page, base_url)
    
    async def test_complete_auth_flow(self, username: str = "testuser", password: str = "testpass123"):
        """Test complete authentication flow."""
        # 1. Navigate to login page
        await self.login_page.navigate()
        await self.login_page.verify_page_elements()
        
        # 2. Test invalid login
        await self.login_page.login_with_invalid_credentials()
        
        # 3. Test valid login
        await self.login_page.login_with_valid_credentials(username, password)
        
        # 4. Verify dashboard access
        await self.verify_dashboard_access()
        
        # 5. Test logout
        await self.logout_page.logout()
        
        # 6. Verify redirect to login
        await self.login_page.verify_page_elements()
    
    async def verify_dashboard_access(self):
        """Verify user can access dashboard after login."""
        # Should be on dashboard
        current_url = self.page.url
        assert "/dashboard/" in current_url
        
        # Check for dashboard elements
        dashboard_indicators = [
            "h1, h2, .page-title",  # Page title
            ".card, .widget, .dashboard-card",  # Dashboard cards
            ".nav, .navbar, .sidebar"  # Navigation
        ]
        
        found_indicator = False
        for indicator in dashboard_indicators:
            if await self.is_visible(indicator):
                found_indicator = True
                break
        
        assert found_indicator, "No dashboard indicators found"
        
        # Verify no console errors
        await self.verify_no_console_errors()
    
    async def test_session_persistence(self):
        """Test that session persists across page reloads."""
        # Login first
        await self.login_page.login_with_valid_credentials()
        
        # Reload page
        await self.page.reload()
        await self.wait_for_page_load()
        
        # Should still be authenticated
        current_url = self.page.url
        assert "/accounts/login/" not in current_url
        
        # Verify no console errors after reload
        await self.verify_no_console_errors()
    
    async def test_protected_page_access(self):
        """Test that protected pages redirect to login when not authenticated."""
        protected_urls = [
            "/dashboard/",
            "/customers/",
            "/transactions/",
            "/reports/"
        ]
        
        for url in protected_urls:
            await self.goto(url)
            
            # Should redirect to login
            await self.page.wait_for_url(f"{self.base_url}/accounts/login/")
            
            # Verify login form is visible
            await expect(self.page.locator("#id_username")).to_be_visible()
    
    async def test_remember_me_functionality(self):
        """Test remember me checkbox if present."""
        await self.login_page.navigate()
        
        # Check if remember me checkbox exists
        remember_me_selector = 'input[name="remember_me"], input[type="checkbox"]'
        if await self.is_visible(remember_me_selector):
            # Test with remember me checked
            await self.page.check(remember_me_selector)
            await self.login_page.login_with_valid_credentials()
            
            # Close browser and reopen (simulate)
            # This would require more complex setup for full testing
            print("Remember me functionality detected but full testing requires browser restart")
    
    async def verify_security_headers(self):
        """Verify security headers are present."""
        response = await self.page.goto(f"{self.base_url}/accounts/login/")
        
        # Check for common security headers
        headers = response.headers
        
        security_checks = {
            'x-frame-options': 'Clickjacking protection',
            'x-content-type-options': 'MIME type sniffing protection',
            'x-xss-protection': 'XSS protection',
        }
        
        for header, description in security_checks.items():
            if header in headers:
                print(f"✅ {description}: {headers[header]}")
            else:
                print(f"⚠️  Missing {description} header")
    
    async def test_csrf_protection(self):
        """Test CSRF protection on login form."""
        await self.login_page.navigate()
        
        # Check for CSRF token
        csrf_token = await self.page.locator('input[name="csrfmiddlewaretoken"]').count()
        assert csrf_token > 0, "CSRF token not found in login form"
        
        print("✅ CSRF protection verified")

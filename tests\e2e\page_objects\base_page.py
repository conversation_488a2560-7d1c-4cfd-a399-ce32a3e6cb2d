"""
Base page object for Arena Doviz E2E tests
"""

import asyncio
from playwright.async_api import Page, expect


class BasePage:
    """Base page object with common functionality."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        self.page = page
        self.base_url = base_url
    
    async def goto(self, path: str = ""):
        """Navigate to a specific path."""
        url = f"{self.base_url}{path}"
        await self.page.goto(url)
        await self.wait_for_page_load()
    
    async def wait_for_page_load(self):
        """Wait for page to fully load."""
        # Wait for network to be idle
        await self.page.wait_for_load_state('networkidle')
        
        # Wait for jQuery to be available
        await self.page.wait_for_function("typeof $ !== 'undefined'")
        
        # Wait for ArenaDoviz object to be available
        await self.page.wait_for_function("typeof ArenaDoviz !== 'undefined'")
    
    async def wait_for_element(self, selector: str, timeout: int = 10000):
        """Wait for element to be visible."""
        await self.page.wait_for_selector(selector, timeout=timeout)
    
    async def click_and_wait(self, selector: str, wait_for: str = None):
        """Click element and wait for navigation or specific element."""
        await self.page.click(selector)
        if wait_for:
            await self.wait_for_element(wait_for)
        else:
            await self.page.wait_for_load_state('networkidle')
    
    async def fill_form_field(self, selector: str, value: str):
        """Fill form field with proper waiting."""
        await self.wait_for_element(selector)
        await self.page.fill(selector, value)
    
    async def select_option(self, selector: str, value: str):
        """Select option from dropdown."""
        await self.wait_for_element(selector)
        await self.page.select_option(selector, value)
    
    async def get_text(self, selector: str) -> str:
        """Get text content of element."""
        await self.wait_for_element(selector)
        return await self.page.text_content(selector)
    
    async def is_visible(self, selector: str) -> bool:
        """Check if element is visible."""
        try:
            await self.page.wait_for_selector(selector, timeout=1000)
            return await self.page.is_visible(selector)
        except:
            return False
    
    async def wait_for_alert(self, alert_type: str = None) -> str:
        """Wait for alert message and return its text."""
        if alert_type:
            selector = f".alert.alert-{alert_type}"
        else:
            selector = ".alert"
        
        await self.wait_for_element(selector)
        return await self.get_text(selector)
    
    async def check_console_errors(self) -> list:
        """Check for JavaScript console errors."""
        return await self.page.evaluate("""
            () => {
                if (window.errorMonitor) {
                    return window.errorMonitor.getErrorReport();
                }
                return { errors: [], domErrors: [], apiErrors: [] };
            }
        """)
    
    async def clear_console_errors(self):
        """Clear JavaScript console errors."""
        await self.page.evaluate("""
            () => {
                if (window.errorMonitor) {
                    window.errorMonitor.clearErrors();
                }
            }
        """)
    
    async def wait_for_ajax_complete(self):
        """Wait for all AJAX requests to complete."""
        await self.page.wait_for_function("""
            () => {
                return typeof $ !== 'undefined' && $.active === 0;
            }
        """)
    
    async def take_screenshot(self, name: str):
        """Take screenshot for debugging."""
        await self.page.screenshot(path=f"screenshots/{name}.png")
    
    async def get_page_title(self) -> str:
        """Get page title."""
        return await self.page.title()
    
    async def verify_no_console_errors(self):
        """Verify there are no console errors."""
        errors = await self.check_console_errors()
        
        if errors['errors'] or errors['domErrors'] or errors['apiErrors']:
            error_summary = {
                'javascript_errors': len(errors['errors']),
                'dom_errors': len(errors['domErrors']),
                'api_errors': len(errors['apiErrors']),
                'details': errors
            }
            raise AssertionError(f"Console errors found: {error_summary}")
    
    async def verify_page_loaded(self):
        """Verify page loaded correctly without errors."""
        # Check that basic elements are present
        await expect(self.page.locator('body')).to_be_visible()
        
        # Check for error pages
        error_indicators = [
            '.error-page',
            '.alert-danger',
            'h1:has-text("Server Error")',
            'h1:has-text("Not Found")'
        ]
        
        for indicator in error_indicators:
            if await self.is_visible(indicator):
                error_text = await self.get_text(indicator)
                raise AssertionError(f"Error page detected: {error_text}")
    
    async def wait_for_form_submission(self):
        """Wait for form submission to complete."""
        # Wait for any loading indicators to disappear
        loading_selectors = [
            '.loading',
            '.spinner',
            '[disabled]',
            '.btn:has-text("Loading")',
            '.btn:has-text("Submitting")'
        ]
        
        for selector in loading_selectors:
            try:
                await self.page.wait_for_selector(selector, state='hidden', timeout=1000)
            except:
                pass  # Selector might not exist
        
        # Wait for AJAX to complete
        await self.wait_for_ajax_complete()
    
    async def verify_form_validation(self, field_selector: str, expected_error: str = None):
        """Verify form field validation."""
        # Check for validation classes
        field = self.page.locator(field_selector)
        
        # Check for is-invalid class
        if await field.get_attribute('class') and 'is-invalid' in await field.get_attribute('class'):
            if expected_error:
                # Look for validation message
                error_selector = f"{field_selector} + .invalid-feedback, {field_selector} ~ .invalid-feedback"
                if await self.is_visible(error_selector):
                    error_text = await self.get_text(error_selector)
                    assert expected_error in error_text, f"Expected error '{expected_error}' not found in '{error_text}'"
            return True
        
        return False
    
    async def upload_file(self, file_input_selector: str, file_path: str):
        """Upload file to file input."""
        await self.wait_for_element(file_input_selector)
        await self.page.set_input_files(file_input_selector, file_path)
    
    async def wait_for_table_load(self, table_selector: str = "table"):
        """Wait for data table to load."""
        await self.wait_for_element(table_selector)
        
        # Wait for DataTables to initialize if present
        await self.page.wait_for_function(f"""
            () => {{
                const table = document.querySelector('{table_selector}');
                if (table && $.fn.DataTable && $.fn.DataTable.isDataTable(table)) {{
                    return !$(table).DataTable().processing();
                }}
                return true;
            }}
        """)
    
    async def get_table_row_count(self, table_selector: str = "table") -> int:
        """Get number of rows in table."""
        await self.wait_for_table_load(table_selector)
        rows = await self.page.locator(f"{table_selector} tbody tr").count()
        return rows
    
    async def search_table(self, search_term: str, search_input_selector: str = ".dataTables_filter input"):
        """Search in data table."""
        await self.wait_for_element(search_input_selector)
        await self.page.fill(search_input_selector, search_term)
        await self.page.press(search_input_selector, 'Enter')
        await self.wait_for_ajax_complete()
    
    async def verify_responsive_design(self):
        """Verify page works on different screen sizes."""
        viewports = [
            {'width': 1920, 'height': 1080},  # Desktop
            {'width': 1024, 'height': 768},   # Tablet
            {'width': 375, 'height': 667},    # Mobile
        ]
        
        for viewport in viewports:
            await self.page.set_viewport_size(viewport)
            await asyncio.sleep(0.5)  # Allow time for responsive changes
            
            # Verify page is still functional
            await self.verify_page_loaded()
            
            # Check for horizontal scrollbars (usually indicates responsive issues)
            scroll_width = await self.page.evaluate("document.body.scrollWidth")
            client_width = await self.page.evaluate("document.body.clientWidth")
            
            if scroll_width > client_width + 10:  # Allow small tolerance
                print(f"Warning: Horizontal scroll detected at {viewport['width']}x{viewport['height']}")
        
        # Reset to default viewport
        await self.page.set_viewport_size({'width': 1920, 'height': 1080})

    async def verify_static_files_loaded(self):
        """Verify that critical static files are loaded."""
        # Check for CSS files
        css_loaded = await self.page.evaluate("""
            () => {
                const stylesheets = Array.from(document.styleSheets);
                return stylesheets.some(sheet =>
                    sheet.href && sheet.href.includes('arena-doviz.css')
                );
            }
        """)

        if not css_loaded:
            raise AssertionError("Arena Doviz CSS not loaded")

        # Check for JavaScript files
        js_loaded = await self.page.evaluate("""
            () => {
                return typeof ArenaDoviz !== 'undefined' &&
                       typeof $ !== 'undefined';
            }
        """)

        if not js_loaded:
            raise AssertionError("Required JavaScript libraries not loaded")

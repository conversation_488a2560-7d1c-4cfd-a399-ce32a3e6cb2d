"""
Dashboard page objects for Arena Doviz E2E tests
"""

from playwright.async_api import Page, expect
from .base_page import BasePage


class DashboardPage(BasePage):
    """Dashboard page object."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.url = "/dashboard/"
        
        # Selectors
        self.page_title = "h1, h2, .page-title"
        self.stats_cards = ".card, .widget, .dashboard-card"
        self.navigation = ".nav, .navbar, .sidebar"
        self.charts_container = ".chart-container, canvas"
        self.recent_transactions = ".recent-transactions, .transaction-list"
        self.quick_actions = ".quick-actions, .action-buttons"
        
        # Specific dashboard metrics
        self.total_customers = "#total-customers"
        self.today_transactions = "#today-transactions"
        self.active_locations = "#active-locations"
        self.pending_approvals = "#pending-approvals"
        
        # Navigation links
        self.customers_link = 'a[href*="/customers/"]'
        self.transactions_link = 'a[href*="/transactions/"]'
        self.reports_link = 'a[href*="/reports/"]'
    
    async def navigate(self):
        """Navigate to dashboard page."""
        await self.goto(self.url)
        await self.verify_page_loaded()
    
    async def verify_dashboard_elements(self):
        """Verify all expected dashboard elements are present."""
        # Check page title
        await expect(self.page.locator(self.page_title)).to_be_visible()
        
        # Check navigation
        await expect(self.page.locator(self.navigation)).to_be_visible()
        
        # Check for at least one stats card
        stats_cards = await self.page.locator(self.stats_cards).count()
        assert stats_cards > 0, "No dashboard stats cards found"
        
        # Verify static files loaded
        await self.verify_static_files_loaded()
        
        # Verify no console errors
        await self.verify_no_console_errors()
    
    async def verify_dashboard_metrics(self):
        """Verify dashboard metrics are displayed correctly."""
        metrics = [
            (self.total_customers, "Total Customers"),
            (self.today_transactions, "Today's Transactions"),
            (self.active_locations, "Active Locations"),
            (self.pending_approvals, "Pending Approvals")
        ]
        
        for selector, name in metrics:
            if await self.is_visible(selector):
                value = await self.get_text(selector)
                # Should be a number or dash
                assert value.isdigit() or value == '-' or value == '0', f"{name} metric shows invalid value: {value}"
                print(f"✅ {name}: {value}")
            else:
                print(f"⚠️  {name} metric not found")
    
    async def verify_charts_functionality(self):
        """Verify dashboard charts are working."""
        # Wait for charts to load
        if await self.is_visible(self.charts_container):
            # Check if Chart.js is loaded
            chart_js_loaded = await self.page.evaluate("""
                () => typeof Chart !== 'undefined'
            """)
            
            if chart_js_loaded:
                # Check if charts are rendered
                charts_rendered = await self.page.evaluate("""
                    () => {
                        const canvases = document.querySelectorAll('canvas');
                        return Array.from(canvases).some(canvas => 
                            canvas.getContext('2d').getImageData(0, 0, 1, 1).data.some(pixel => pixel !== 0)
                        );
                    }
                """)
                
                if charts_rendered:
                    print("✅ Dashboard charts are rendered")
                else:
                    print("⚠️  Dashboard charts found but not rendered")
            else:
                print("⚠️  Chart.js not loaded")
        else:
            print("ℹ️  No charts found on dashboard")
    
    async def test_navigation_links(self):
        """Test navigation links from dashboard."""
        navigation_tests = [
            (self.customers_link, "/customers/", "Customers"),
            (self.transactions_link, "/transactions/", "Transactions"),
            (self.reports_link, "/reports/", "Reports")
        ]
        
        for link_selector, expected_url_part, section_name in navigation_tests:
            if await self.is_visible(link_selector):
                # Click the link
                await self.click_and_wait(link_selector)
                
                # Verify navigation
                current_url = self.page.url
                assert expected_url_part in current_url, f"Navigation to {section_name} failed"
                
                # Verify page loaded without errors
                await self.verify_page_loaded()
                await self.verify_no_console_errors()
                
                print(f"✅ Navigation to {section_name} successful")
                
                # Go back to dashboard
                await self.navigate()
            else:
                print(f"⚠️  {section_name} link not found")
    
    async def verify_real_time_updates(self):
        """Verify real-time updates functionality."""
        # Check if real-time updates are configured
        realtime_active = await self.page.evaluate("""
            () => {
                return typeof ArenaDoviz !== 'undefined' && 
                       ArenaDoviz.realtime && 
                       typeof ArenaDoviz.realtime.start === 'function';
            }
        """)
        
        if realtime_active:
            print("✅ Real-time updates configured")
            
            # Test update dashboard function
            update_function_exists = await self.page.evaluate("""
                () => {
                    return typeof ArenaDoviz.realtime.updateDashboard === 'function';
                }
            """)
            
            if update_function_exists:
                print("✅ Dashboard update function available")
            else:
                print("⚠️  Dashboard update function not found")
        else:
            print("ℹ️  Real-time updates not configured")
    
    async def test_responsive_dashboard(self):
        """Test dashboard responsiveness."""
        await self.verify_responsive_design()
        
        # Test specific dashboard elements at different sizes
        viewports = [
            {'width': 1920, 'height': 1080},  # Desktop
            {'width': 1024, 'height': 768},   # Tablet
            {'width': 375, 'height': 667},    # Mobile
        ]
        
        for viewport in viewports:
            await self.page.set_viewport_size(viewport)
            await asyncio.sleep(0.5)
            
            # Check if stats cards are still visible and properly arranged
            stats_visible = await self.is_visible(self.stats_cards)
            assert stats_visible, f"Stats cards not visible at {viewport['width']}x{viewport['height']}"
            
            # Check navigation accessibility
            nav_visible = await self.is_visible(self.navigation)
            assert nav_visible, f"Navigation not accessible at {viewport['width']}x{viewport['height']}"
            
            print(f"✅ Dashboard responsive at {viewport['width']}x{viewport['height']}")
        
        # Reset viewport
        await self.page.set_viewport_size({'width': 1920, 'height': 1080})
    
    async def verify_user_info_display(self):
        """Verify user information is displayed correctly."""
        user_info_selectors = [
            ".user-name, .username",
            ".user-role, .role",
            ".user-location, .location",
            ".user-menu, .dropdown-toggle"
        ]
        
        user_info_found = False
        for selector in user_info_selectors:
            if await self.is_visible(selector):
                user_info = await self.get_text(selector)
                if user_info and user_info.strip():
                    print(f"✅ User info found: {user_info}")
                    user_info_found = True
                    break
        
        if not user_info_found:
            print("⚠️  User information not displayed")
    
    async def test_quick_actions(self):
        """Test quick action buttons if present."""
        if await self.is_visible(self.quick_actions):
            # Get all quick action buttons
            action_buttons = await self.page.locator(f"{self.quick_actions} button, {self.quick_actions} a").count()
            
            if action_buttons > 0:
                print(f"✅ Found {action_buttons} quick action buttons")
                
                # Test first action button (without actually performing the action)
                first_button = self.page.locator(f"{self.quick_actions} button, {self.quick_actions} a").first
                button_text = await first_button.text_content()
                
                # Verify button is clickable
                await expect(first_button).to_be_enabled()
                print(f"✅ Quick action '{button_text}' is clickable")
            else:
                print("ℹ️  No quick action buttons found")
        else:
            print("ℹ️  Quick actions section not found")
    
    async def verify_recent_transactions(self):
        """Verify recent transactions display."""
        if await self.is_visible(self.recent_transactions):
            # Check if transactions are displayed
            transaction_rows = await self.page.locator(f"{self.recent_transactions} tr, {self.recent_transactions} .transaction-item").count()
            
            if transaction_rows > 0:
                print(f"✅ Found {transaction_rows} recent transactions")
                
                # Verify transaction data format
                first_transaction = self.page.locator(f"{self.recent_transactions} tr, {self.recent_transactions} .transaction-item").first
                transaction_text = await first_transaction.text_content()
                
                # Should contain some transaction-like data
                transaction_indicators = ['$', '€', '£', 'USD', 'AED', 'IRR', '#']
                has_transaction_data = any(indicator in transaction_text for indicator in transaction_indicators)
                
                if has_transaction_data:
                    print("✅ Recent transactions contain valid data")
                else:
                    print("⚠️  Recent transactions may not contain valid data")
            else:
                print("ℹ️  No recent transactions displayed")
        else:
            print("ℹ️  Recent transactions section not found")
    
    async def test_dashboard_performance(self):
        """Test dashboard loading performance."""
        # Measure page load time
        start_time = await self.page.evaluate("performance.now()")
        await self.navigate()
        end_time = await self.page.evaluate("performance.now()")
        
        load_time = end_time - start_time
        print(f"📊 Dashboard load time: {load_time:.2f}ms")
        
        # Check for performance issues
        if load_time > 5000:  # 5 seconds
            print("⚠️  Dashboard load time is slow (>5s)")
        elif load_time > 2000:  # 2 seconds
            print("⚠️  Dashboard load time is moderate (>2s)")
        else:
            print("✅ Dashboard load time is good (<2s)")
        
        # Check for memory leaks (basic check)
        memory_info = await self.page.evaluate("""
            () => {
                if (performance.memory) {
                    return {
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit
                    };
                }
                return null;
            }
        """)
        
        if memory_info:
            memory_usage_mb = memory_info['used'] / (1024 * 1024)
            print(f"📊 Memory usage: {memory_usage_mb:.2f}MB")
            
            if memory_usage_mb > 100:  # 100MB
                print("⚠️  High memory usage detected")
            else:
                print("✅ Memory usage is reasonable")
    
    async def run_comprehensive_dashboard_test(self):
        """Run all dashboard tests."""
        print("🚀 Running Comprehensive Dashboard Tests")
        print("=" * 50)
        
        await self.navigate()
        
        # Basic functionality tests
        await self.verify_dashboard_elements()
        await self.verify_dashboard_metrics()
        await self.verify_user_info_display()
        
        # Feature tests
        await self.verify_charts_functionality()
        await self.test_quick_actions()
        await self.verify_recent_transactions()
        await self.verify_real_time_updates()
        
        # Navigation tests
        await self.test_navigation_links()
        
        # Responsive design tests
        await self.test_responsive_dashboard()
        
        # Performance tests
        await self.test_dashboard_performance()
        
        print("✅ Dashboard tests completed")

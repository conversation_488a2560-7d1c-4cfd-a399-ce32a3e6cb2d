"""
Transaction page objects for Arena Doviz E2E tests
"""

import asyncio
from playwright.async_api import Page, expect
from .base_page import BasePage


class TransactionBasePage(BasePage):
    """Base class for transaction pages."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        
        # Common form selectors
        self.transaction_form = "#transaction-form"
        self.customer_select = "#customer"
        self.location_select = "#location"
        self.from_currency_select = "#from_currency"
        self.to_currency_select = "#to_currency"
        self.from_amount_input = "#from_amount"
        self.to_amount_input = "#to_amount"
        self.exchange_rate_input = "#exchange_rate"
        self.commission_amount_input = "#commission_amount"
        self.notes_textarea = "#notes"
        self.delivery_method_select = "#delivery_method"
        self.courier_select = "#courier"
        self.submit_button = 'button[type="submit"]'
        self.save_draft_button = 'button[data-action="save"]'
        
        # Common elements
        self.customer_balance = "#customer-balance"
        self.transaction_preview = "#transaction-preview"
        self.alert_container = ".alert"
        self.loading_indicator = ".loading, .spinner"
    
    async def wait_for_form_load(self):
        """Wait for transaction form to fully load."""
        await self.wait_for_element(self.transaction_form)
        await self.wait_for_ajax_complete()
        
        # Wait for dropdowns to be populated
        await self.page.wait_for_function("""
            () => {
                const customer = document.querySelector('#customer');
                const currency = document.querySelector('#from_currency');
                const location = document.querySelector('#location');
                
                return customer && customer.options.length > 1 &&
                       currency && currency.options.length > 1 &&
                       location && location.options.length > 1;
            }
        """)
    
    async def select_customer(self, customer_name: str = None):
        """Select a customer from dropdown."""
        await self.wait_for_element(self.customer_select)
        
        if customer_name:
            # Select specific customer
            await self.page.select_option(self.customer_select, label=customer_name)
        else:
            # Select first available customer
            await self.page.select_option(self.customer_select, index=1)
        
        # Wait for customer balance to load
        await self.wait_for_ajax_complete()
    
    async def select_location(self, location_name: str = None):
        """Select a location from dropdown."""
        await self.wait_for_element(self.location_select)
        
        if location_name:
            await self.page.select_option(self.location_select, label=location_name)
        else:
            await self.page.select_option(self.location_select, index=1)
    
    async def select_currencies(self, from_currency: str = "USD", to_currency: str = "AED"):
        """Select from and to currencies."""
        await self.wait_for_element(self.from_currency_select)
        await self.wait_for_element(self.to_currency_select)
        
        await self.page.select_option(self.from_currency_select, label=from_currency)
        await self.page.select_option(self.to_currency_select, label=to_currency)
        
        # Wait for exchange rate to load
        await self.wait_for_ajax_complete()
    
    async def enter_amount(self, amount: str):
        """Enter transaction amount."""
        await self.fill_form_field(self.from_amount_input, amount)
        
        # Wait for calculations to complete
        await asyncio.sleep(0.5)
    
    async def verify_courier_functionality(self):
        """Test courier selection functionality."""
        # Set delivery method to courier
        if await self.is_visible(self.delivery_method_select):
            await self.page.select_option(self.delivery_method_select, "courier")
            
            # Courier field should become visible
            await self.wait_for_element(self.courier_select)
            
            # Check if couriers are loaded
            courier_options = await self.page.locator(f"{self.courier_select} option").count()
            assert courier_options > 1, "No courier options available"
            
            # Select a courier
            await self.page.select_option(self.courier_select, index=1)
            
            print("✅ Courier functionality working")
        else:
            print("ℹ️  Delivery method not available for this transaction type")
    
    async def submit_transaction(self, as_draft: bool = False):
        """Submit transaction form."""
        if as_draft and await self.is_visible(self.save_draft_button):
            await self.click_and_wait(self.save_draft_button)
        else:
            await self.click_and_wait(self.submit_button)
        
        await self.wait_for_form_submission()
    
    async def verify_form_validation(self):
        """Test form validation."""
        # Try to submit empty form
        await self.click_and_wait(self.submit_button)
        
        # Check for validation errors
        required_fields = [
            self.customer_select,
            self.from_currency_select,
            self.to_currency_select,
            self.from_amount_input
        ]
        
        validation_found = False
        for field in required_fields:
            if await self.verify_form_validation(field):
                validation_found = True
        
        if not validation_found:
            # Check for alert messages
            if await self.is_visible(self.alert_container):
                alert_text = await self.get_text(self.alert_container)
                assert "required" in alert_text.lower() or "error" in alert_text.lower()
                validation_found = True
        
        assert validation_found, "No form validation detected"
        print("✅ Form validation working")
    
    async def verify_transaction_preview(self):
        """Verify transaction preview updates."""
        if await self.is_visible(self.transaction_preview):
            # Fill form partially
            await self.select_customer()
            await self.select_currencies()
            await self.enter_amount("100")
            
            # Check if preview updates
            await asyncio.sleep(1)
            preview_text = await self.get_text(self.transaction_preview)
            
            # Should contain transaction details
            assert "100" in preview_text or "$" in preview_text or "USD" in preview_text
            print("✅ Transaction preview updating")
        else:
            print("ℹ️  Transaction preview not available")


class CurrencyExchangePage(TransactionBasePage):
    """Currency Exchange transaction page."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.url = "/transactions/type/EXCHANGE/"
    
    async def navigate(self):
        """Navigate to currency exchange page."""
        await self.goto(self.url)
        await self.wait_for_form_load()
    
    async def create_exchange_transaction(self, amount: str = "100", from_currency: str = "USD", to_currency: str = "AED"):
        """Create a complete currency exchange transaction."""
        await self.navigate()
        
        # Fill form
        await self.select_customer()
        await self.select_location()
        await self.select_currencies(from_currency, to_currency)
        await self.enter_amount(amount)
        
        # Verify calculations
        await self.verify_exchange_calculations()
        
        # Submit transaction
        await self.submit_transaction()
        
        # Verify success
        await self.verify_transaction_success()
    
    async def verify_exchange_calculations(self):
        """Verify exchange rate calculations."""
        # Get values
        from_amount = await self.page.input_value(self.from_amount_input)
        to_amount = await self.page.input_value(self.to_amount_input)
        exchange_rate = await self.page.input_value(self.exchange_rate_input)
        
        if from_amount and to_amount and exchange_rate:
            # Verify calculation: to_amount = from_amount * exchange_rate
            calculated = float(from_amount) * float(exchange_rate)
            actual = float(to_amount)
            
            # Allow small rounding differences
            assert abs(calculated - actual) < 0.01, f"Exchange calculation error: {calculated} != {actual}"
            print("✅ Exchange rate calculations correct")
    
    async def verify_transaction_success(self):
        """Verify transaction was created successfully."""
        # Should show success message or redirect
        if await self.is_visible(self.alert_container):
            alert_text = await self.get_text(self.alert_container)
            assert "success" in alert_text.lower() or "created" in alert_text.lower()
        
        # Or should redirect to transaction list
        current_url = self.page.url
        if "/transactions/" in current_url and "add" not in current_url:
            print("✅ Redirected to transaction list")
        
        await self.verify_no_console_errors()


class CashDepositPage(TransactionBasePage):
    """Cash Deposit transaction page."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.url = "/transactions/type/DEPOSIT/"
        
        # Deposit-specific selectors
        self.deposit_source_select = "#deposit_source"
        self.bank_reference_input = "#bank_reference"
        self.cash_verified_checkbox = "#cash_verified"
        self.customer_id_verified_checkbox = "#customer_id_verified"
        self.received_by_input = "#received_by"
        self.receipt_number_input = "#receipt_number"
    
    async def navigate(self):
        """Navigate to cash deposit page."""
        await self.goto(self.url)
        await self.wait_for_form_load()
    
    async def create_deposit_transaction(self, amount: str = "1000", currency: str = "USD"):
        """Create a complete cash deposit transaction."""
        await self.navigate()
        
        # Fill form
        await self.select_customer()
        await self.select_location()
        
        # For deposits, from and to currency are the same
        await self.page.select_option(self.from_currency_select, label=currency)
        await self.enter_amount(amount)
        
        # Deposit-specific fields
        if await self.is_visible(self.deposit_source_select):
            await self.page.select_option(self.deposit_source_select, "cash")
        
        # Verification checkboxes
        if await self.is_visible(self.cash_verified_checkbox):
            await self.page.check(self.cash_verified_checkbox)
        
        if await self.is_visible(self.customer_id_verified_checkbox):
            await self.page.check(self.customer_id_verified_checkbox)
        
        # Test courier functionality
        await self.verify_courier_functionality()
        
        # Submit transaction
        await self.submit_transaction()
        
        # Verify success
        await self.verify_transaction_success()


class MoneyTransferPage(TransactionBasePage):
    """Money Transfer transaction page."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.url = "/transactions/type/TRANSFER/"
        
        # Transfer-specific selectors
        self.recipient_name_input = "#recipient_name"
        self.recipient_phone_input = "#recipient_phone"
        self.recipient_address_textarea = "#recipient_address"
        self.transfer_purpose_select = "#transfer_purpose"
    
    async def navigate(self):
        """Navigate to money transfer page."""
        await self.goto(self.url)
        await self.wait_for_form_load()
    
    async def create_transfer_transaction(self, amount: str = "500", currency: str = "USD"):
        """Create a complete money transfer transaction."""
        await self.navigate()
        
        # Fill form
        await self.select_customer()
        await self.select_location()
        
        # For transfers, from and to currency are usually the same
        await self.page.select_option(self.from_currency_select, label=currency)
        await self.enter_amount(amount)
        
        # Transfer-specific fields
        if await self.is_visible(self.recipient_name_input):
            await self.fill_form_field(self.recipient_name_input, "John Recipient")
        
        if await self.is_visible(self.recipient_phone_input):
            await self.fill_form_field(self.recipient_phone_input, "+1234567890")
        
        if await self.is_visible(self.transfer_purpose_select):
            await self.page.select_option(self.transfer_purpose_select, index=1)
        
        # Test courier functionality
        await self.verify_courier_functionality()
        
        # Submit transaction
        await self.submit_transaction()
        
        # Verify success
        await self.verify_transaction_success()


class TransactionListPage(BasePage):
    """Transaction list/management page."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        super().__init__(page, base_url)
        self.url = "/transactions/"
        
        # Selectors
        self.transactions_table = "#transactions-table"
        self.search_input = ".dataTables_filter input"
        self.filter_form = "#filter-form"
        self.add_transaction_button = 'a[href*="add"], .btn:has-text("Add")'
        self.export_button = "#export-btn"
        self.refresh_button = "#refresh-btn"
    
    async def navigate(self):
        """Navigate to transactions list page."""
        await self.goto(self.url)
        await self.wait_for_table_load(self.transactions_table)
    
    async def verify_transaction_list(self):
        """Verify transaction list functionality."""
        await self.navigate()
        
        # Check table exists
        await expect(self.page.locator(self.transactions_table)).to_be_visible()
        
        # Check for data
        row_count = await self.get_table_row_count(self.transactions_table)
        print(f"📊 Found {row_count} transactions in table")
        
        # Test search functionality
        if await self.is_visible(self.search_input):
            await self.search_table("test", self.search_input)
            print("✅ Search functionality working")
        
        # Test export button
        if await self.is_visible(self.export_button):
            await expect(self.page.locator(self.export_button)).to_be_enabled()
            print("✅ Export button available")
        
        await self.verify_no_console_errors()
    
    async def test_transaction_creation_flow(self):
        """Test complete transaction creation flow."""
        await self.navigate()
        
        # Click add transaction button
        if await self.is_visible(self.add_transaction_button):
            await self.click_and_wait(self.add_transaction_button)
            
            # Should navigate to transaction form
            current_url = self.page.url
            assert "add" in current_url or "create" in current_url
            
            print("✅ Add transaction navigation working")
        else:
            print("⚠️  Add transaction button not found")


class TransactionTestSuite:
    """Complete transaction testing suite."""
    
    def __init__(self, page: Page, base_url: str = "http://localhost:8000"):
        self.page = page
        self.base_url = base_url
        
        # Initialize page objects
        self.exchange_page = CurrencyExchangePage(page, base_url)
        self.deposit_page = CashDepositPage(page, base_url)
        self.transfer_page = MoneyTransferPage(page, base_url)
        self.list_page = TransactionListPage(page, base_url)
    
    async def run_comprehensive_transaction_tests(self):
        """Run all transaction tests."""
        print("🚀 Running Comprehensive Transaction Tests")
        print("=" * 50)
        
        # Test transaction list
        await self.list_page.verify_transaction_list()
        await self.list_page.test_transaction_creation_flow()
        
        # Test currency exchange
        print("\n💱 Testing Currency Exchange")
        await self.exchange_page.create_exchange_transaction()
        await self.exchange_page.verify_form_validation()
        
        # Test cash deposit
        print("\n💰 Testing Cash Deposit")
        await self.deposit_page.create_deposit_transaction()
        await self.deposit_page.verify_form_validation()
        
        # Test money transfer
        print("\n💸 Testing Money Transfer")
        await self.transfer_page.create_transfer_transaction()
        await self.transfer_page.verify_form_validation()
        
        print("\n✅ All transaction tests completed")

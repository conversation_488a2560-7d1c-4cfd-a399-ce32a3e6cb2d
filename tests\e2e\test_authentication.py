"""
Authentication E2E tests for Arena Doviz
"""

import pytest
from page_objects.auth_page import LoginPage, LogoutPage, AuthenticationFlow


@pytest.mark.asyncio
class TestAuthentication:
    """Test authentication functionality."""
    
    async def test_login_page_elements(self, page, server_url):
        """Test login page has all required elements."""
        login_page = LoginPage(page, server_url)
        await login_page.verify_page_elements()
    
    async def test_valid_login(self, page, server_url, test_user):
        """Test login with valid credentials."""
        login_page = LoginPage(page, server_url)
        await login_page.login_with_valid_credentials(test_user.username, 'testpass123')
    
    async def test_invalid_login(self, page, server_url):
        """Test login with invalid credentials."""
        login_page = LoginPage(page, server_url)
        await login_page.login_with_invalid_credentials()
    
    async def test_login_form_validation(self, page, server_url):
        """Test login form validation."""
        login_page = LoginPage(page, server_url)
        await login_page.verify_login_form_validation()
    
    async def test_logout_functionality(self, authenticated_page, server_url):
        """Test logout functionality."""
        logout_page = LogoutPage(authenticated_page, server_url)
        await logout_page.verify_logout_redirects_to_login()
    
    async def test_complete_auth_flow(self, page, server_url, test_user):
        """Test complete authentication flow."""
        auth_flow = AuthenticationFlow(page, server_url)
        await auth_flow.test_complete_auth_flow(test_user.username, 'testpass123')
    
    async def test_session_persistence(self, page, server_url, test_user):
        """Test session persistence across page reloads."""
        auth_flow = AuthenticationFlow(page, server_url)
        login_page = LoginPage(page, server_url)
        
        # Login first
        await login_page.login_with_valid_credentials(test_user.username, 'testpass123')
        
        # Test session persistence
        await auth_flow.test_session_persistence()
    
    async def test_protected_page_access(self, page, server_url):
        """Test that protected pages redirect to login when not authenticated."""
        auth_flow = AuthenticationFlow(page, server_url)
        await auth_flow.test_protected_page_access()
    
    async def test_csrf_protection(self, page, server_url):
        """Test CSRF protection on login form."""
        auth_flow = AuthenticationFlow(page, server_url)
        await auth_flow.test_csrf_protection()
    
    async def test_security_headers(self, page, server_url):
        """Test security headers are present."""
        auth_flow = AuthenticationFlow(page, server_url)
        await auth_flow.verify_security_headers()
    
    async def test_remember_me_functionality(self, page, server_url):
        """Test remember me functionality if present."""
        auth_flow = AuthenticationFlow(page, server_url)
        await auth_flow.test_remember_me_functionality()


@pytest.mark.asyncio
class TestAuthenticationErrorHandling:
    """Test authentication error handling and edge cases."""
    
    async def test_multiple_failed_login_attempts(self, page, server_url):
        """Test multiple failed login attempts."""
        login_page = LoginPage(page, server_url)
        
        # Try multiple failed logins
        for i in range(3):
            await login_page.navigate()
            await login_page.login_with_invalid_credentials(f"user{i}", f"pass{i}")
            
            # Should still show login form
            await login_page.verify_page_elements()
    
    async def test_empty_credentials(self, page, server_url):
        """Test login with empty credentials."""
        login_page = LoginPage(page, server_url)
        await login_page.navigate()
        
        # Try to login with empty fields
        await login_page.login("", "")
        
        # Should show validation or stay on login page
        current_url = page.url
        assert "/accounts/login/" in current_url
    
    async def test_sql_injection_attempts(self, page, server_url):
        """Test SQL injection attempts in login form."""
        login_page = LoginPage(page, server_url)
        
        sql_injection_attempts = [
            "admin' OR '1'='1",
            "admin'; DROP TABLE users; --",
            "admin' UNION SELECT * FROM users --"
        ]
        
        for attempt in sql_injection_attempts:
            await login_page.navigate()
            await login_page.login(attempt, "password")
            
            # Should not succeed and should not cause errors
            current_url = page.url
            assert "/dashboard/" not in current_url
            
            # Check for no console errors
            await login_page.verify_no_console_errors()
    
    async def test_xss_attempts(self, page, server_url):
        """Test XSS attempts in login form."""
        login_page = LoginPage(page, server_url)
        
        xss_attempts = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>"
        ]
        
        for attempt in xss_attempts:
            await login_page.navigate()
            await login_page.login(attempt, "password")
            
            # Should not execute script
            # Check that no alert dialogs appeared
            try:
                await page.wait_for_event("dialog", timeout=1000)
                assert False, "XSS attempt succeeded - alert dialog appeared"
            except:
                pass  # Good, no alert dialog
            
            # Check for no console errors
            await login_page.verify_no_console_errors()


@pytest.mark.asyncio
class TestAuthenticationAccessibility:
    """Test authentication accessibility features."""
    
    async def test_keyboard_navigation(self, page, server_url):
        """Test keyboard navigation on login form."""
        login_page = LoginPage(page, server_url)
        await login_page.navigate()
        
        # Tab through form elements
        await page.keyboard.press("Tab")  # Should focus username
        focused_element = await page.evaluate("document.activeElement.id")
        assert "username" in focused_element.lower()
        
        await page.keyboard.press("Tab")  # Should focus password
        focused_element = await page.evaluate("document.activeElement.id")
        assert "password" in focused_element.lower()
        
        await page.keyboard.press("Tab")  # Should focus submit button
        focused_element = await page.evaluate("document.activeElement.tagName")
        assert focused_element.lower() in ["button", "input"]
    
    async def test_screen_reader_compatibility(self, page, server_url):
        """Test screen reader compatibility."""
        login_page = LoginPage(page, server_url)
        await login_page.navigate()
        
        # Check for proper labels
        username_label = await page.locator('label[for*="username"]').count()
        password_label = await page.locator('label[for*="password"]').count()
        
        assert username_label > 0, "Username field missing proper label"
        assert password_label > 0, "Password field missing proper label"
        
        # Check for ARIA attributes
        form_element = page.locator("form")
        role = await form_element.get_attribute("role")
        if role:
            assert role == "form"
    
    async def test_high_contrast_mode(self, page, server_url):
        """Test high contrast mode compatibility."""
        login_page = LoginPage(page, server_url)
        await login_page.navigate()
        
        # Enable high contrast mode simulation
        await page.emulate_media(color_scheme="dark")
        
        # Verify page is still usable
        await login_page.verify_page_elements()
        
        # Reset to normal mode
        await page.emulate_media(color_scheme="light")


@pytest.mark.asyncio
class TestAuthenticationPerformance:
    """Test authentication performance."""
    
    async def test_login_performance(self, page, server_url, test_user):
        """Test login performance."""
        login_page = LoginPage(page, server_url)
        
        # Measure login time
        start_time = await page.evaluate("performance.now()")
        await login_page.login_with_valid_credentials(test_user.username, 'testpass123')
        end_time = await page.evaluate("performance.now()")
        
        login_time = end_time - start_time
        print(f"📊 Login time: {login_time:.2f}ms")
        
        # Login should be reasonably fast
        assert login_time < 5000, f"Login too slow: {login_time}ms"
    
    async def test_concurrent_login_attempts(self, browser, server_url, test_user):
        """Test concurrent login attempts."""
        # Create multiple browser contexts
        contexts = []
        pages = []
        
        try:
            for i in range(3):
                context = await browser.new_context()
                page = await context.new_page()
                contexts.append(context)
                pages.append(page)
            
            # Perform concurrent logins
            login_tasks = []
            for page in pages:
                login_page = LoginPage(page, server_url)
                task = login_page.login_with_valid_credentials(test_user.username, 'testpass123')
                login_tasks.append(task)
            
            # Wait for all logins to complete
            import asyncio
            await asyncio.gather(*login_tasks)
            
            # All should succeed
            for page in pages:
                current_url = page.url
                assert "/dashboard/" in current_url
        
        finally:
            # Clean up contexts
            for context in contexts:
                await context.close()


@pytest.mark.asyncio
class TestAuthenticationIntegration:
    """Test authentication integration with other systems."""
    
    async def test_authentication_with_error_monitoring(self, page, server_url, test_user, console_monitor):
        """Test authentication with error monitoring active."""
        login_page = LoginPage(page, server_url)
        
        # Clear any existing errors
        console_monitor['clear']()
        
        # Perform login
        await login_page.login_with_valid_credentials(test_user.username, 'testpass123')
        
        # Check for any console errors during login
        errors = console_monitor['get_errors']()
        assert len(errors) == 0, f"Console errors during login: {errors}"
    
    async def test_authentication_with_network_monitoring(self, page, server_url, test_user, network_monitor):
        """Test authentication with network monitoring."""
        login_page = LoginPage(page, server_url)
        
        # Clear network logs
        network_monitor['clear']()
        
        # Perform login
        await login_page.login_with_valid_credentials(test_user.username, 'testpass123')
        
        # Check for failed requests
        failed_requests = network_monitor['get_failed_requests']()
        
        # Filter out expected 404s (like favicon)
        critical_failures = [req for req in failed_requests 
                           if req['status'] >= 500 or 
                           (req['status'] >= 400 and 'favicon' not in req['url'])]
        
        assert len(critical_failures) == 0, f"Critical network failures during login: {critical_failures}"

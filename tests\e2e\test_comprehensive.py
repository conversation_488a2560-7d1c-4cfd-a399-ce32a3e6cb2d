"""
Comprehensive E2E tests for Arena Doviz system
"""

import pytest
import asyncio
from page_objects.auth_page import AuthenticationFlow
from page_objects.dashboard_page import DashboardPage
from page_objects.transaction_page import TransactionTestSuite


@pytest.mark.asyncio
class TestComprehensiveSystem:
    """Comprehensive system tests covering all major functionality."""
    
    async def test_complete_user_workflow(self, page, server_url, test_user, test_customer, test_currencies, test_locations):
        """Test complete user workflow from login to transaction creation."""
        print("🚀 Starting Complete User Workflow Test")
        print("=" * 60)
        
        # 1. Authentication
        print("\n🔐 Step 1: Authentication")
        auth_flow = AuthenticationFlow(page, server_url)
        await auth_flow.test_complete_auth_flow(test_user.username, 'testpass123')
        
        # 2. Dashboard verification
        print("\n📊 Step 2: Dashboard Verification")
        dashboard = DashboardPage(page, server_url)
        await dashboard.run_comprehensive_dashboard_test()
        
        # 3. Transaction workflow
        print("\n💰 Step 3: Transaction Workflow")
        transaction_suite = TransactionTestSuite(page, server_url)
        await transaction_suite.run_comprehensive_transaction_tests()
        
        # 4. Final verification
        print("\n✅ Step 4: Final System Verification")
        await self.verify_system_health(page)
        
        print("\n🎉 Complete User Workflow Test Passed!")
    
    async def verify_system_health(self, page):
        """Verify overall system health."""
        # Check for JavaScript errors
        error_report = await page.evaluate("""
            () => {
                if (window.errorMonitor) {
                    return window.errorMonitor.getErrorReport();
                }
                return { errors: [], domErrors: [], apiErrors: [], totalErrors: 0 };
            }
        """)
        
        print(f"📊 System Health Report:")
        print(f"   JavaScript Errors: {len(error_report.get('errors', []))}")
        print(f"   DOM Errors: {len(error_report.get('domErrors', []))}")
        print(f"   API Errors: {len(error_report.get('apiErrors', []))}")
        print(f"   Total Errors: {error_report.get('totalErrors', 0)}")
        
        # Assert no critical errors
        assert error_report.get('totalErrors', 0) == 0, f"System has errors: {error_report}"
        
        # Check page performance
        performance_metrics = await page.evaluate("""
            () => {
                const navigation = performance.getEntriesByType('navigation')[0];
                return {
                    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
                    loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
                    totalLoadTime: navigation.loadEventEnd - navigation.fetchStart
                };
            }
        """)
        
        print(f"📈 Performance Metrics:")
        print(f"   DOM Content Loaded: {performance_metrics['domContentLoaded']:.2f}ms")
        print(f"   Load Complete: {performance_metrics['loadComplete']:.2f}ms")
        print(f"   Total Load Time: {performance_metrics['totalLoadTime']:.2f}ms")
        
        # Performance should be reasonable
        assert performance_metrics['totalLoadTime'] < 10000, "Page load time too slow"


@pytest.mark.asyncio
class TestErrorHandlingAndRecovery:
    """Test system error handling and recovery mechanisms."""
    
    async def test_network_error_handling(self, authenticated_page, server_url):
        """Test handling of network errors."""
        print("🌐 Testing Network Error Handling")
        
        # Simulate network failure
        await authenticated_page.route("**/api/v1/**", lambda route: route.abort())
        
        # Try to perform an action that requires API call
        await authenticated_page.goto(f"{server_url}/transactions/")
        
        # Should handle gracefully without crashing
        await asyncio.sleep(2)
        
        # Check for error handling
        error_indicators = await authenticated_page.locator(".alert-danger, .error-message").count()
        
        # Should show some error indication
        if error_indicators == 0:
            print("⚠️  No error indication shown for network failure")
        else:
            print("✅ Network error handled gracefully")
        
        # Remove network simulation
        await authenticated_page.unroute("**/api/v1/**")
    
    async def test_javascript_error_recovery(self, authenticated_page, server_url):
        """Test recovery from JavaScript errors."""
        print("🔧 Testing JavaScript Error Recovery")
        
        # Inject a JavaScript error
        await authenticated_page.evaluate("""
            () => {
                // Simulate an error
                setTimeout(() => {
                    throw new Error('Test error for recovery testing');
                }, 100);
            }
        """)
        
        await asyncio.sleep(0.5)
        
        # Page should still be functional
        await authenticated_page.goto(f"{server_url}/dashboard/")
        
        # Verify basic functionality still works
        dashboard = DashboardPage(authenticated_page, server_url)
        await dashboard.verify_dashboard_elements()
        
        print("✅ System recovered from JavaScript error")
    
    async def test_form_validation_edge_cases(self, authenticated_page, server_url):
        """Test form validation with edge cases."""
        print("📝 Testing Form Validation Edge Cases")
        
        # Go to transaction form
        await authenticated_page.goto(f"{server_url}/transactions/type/EXCHANGE/")
        
        # Test with extreme values
        edge_cases = [
            ("", "Empty value"),
            ("0", "Zero value"),
            ("-100", "Negative value"),
            ("999999999999", "Very large value"),
            ("abc", "Non-numeric value"),
            ("12.345678901", "Too many decimals"),
        ]
        
        for value, description in edge_cases:
            # Clear and fill amount field
            await authenticated_page.fill("#from_amount", "")
            await authenticated_page.fill("#from_amount", value)
            
            # Try to submit
            await authenticated_page.click('button[type="submit"]')
            
            # Should show validation or handle gracefully
            await asyncio.sleep(0.5)
            
            print(f"   Tested {description}: {value}")
        
        print("✅ Form validation edge cases handled")


@pytest.mark.asyncio
class TestCrossBrowserCompatibility:
    """Test cross-browser compatibility."""
    
    @pytest.mark.parametrize("browser_name", ["chromium", "firefox", "webkit"])
    async def test_browser_compatibility(self, browser_name, server_url, test_user):
        """Test compatibility across different browsers."""
        print(f"🌐 Testing {browser_name.title()} Compatibility")
        
        # This would require separate browser instances
        # For now, we'll test with the current browser
        # In a full implementation, you'd create different browser instances
        
        # Basic functionality test
        print(f"✅ {browser_name.title()} compatibility test placeholder")


@pytest.mark.asyncio
class TestAccessibilityCompliance:
    """Test accessibility compliance."""
    
    async def test_wcag_compliance(self, authenticated_page, server_url):
        """Test WCAG compliance basics."""
        print("♿ Testing WCAG Compliance")
        
        # Test keyboard navigation
        await authenticated_page.goto(f"{server_url}/dashboard/")
        
        # Tab through interactive elements
        interactive_elements = await authenticated_page.locator("a, button, input, select, textarea").count()
        
        if interactive_elements > 0:
            # Test first few elements
            for i in range(min(5, interactive_elements)):
                await authenticated_page.keyboard.press("Tab")
                
                # Check if element is focused
                focused_element = await authenticated_page.evaluate("document.activeElement.tagName")
                assert focused_element.lower() in ["a", "button", "input", "select", "textarea"]
        
        print("✅ Basic keyboard navigation working")
        
        # Test color contrast (basic check)
        contrast_issues = await authenticated_page.evaluate("""
            () => {
                const elements = document.querySelectorAll('*');
                let issues = 0;
                
                for (let element of elements) {
                    const style = window.getComputedStyle(element);
                    const color = style.color;
                    const backgroundColor = style.backgroundColor;
                    
                    // Basic check for very light text on light background
                    if (color === 'rgb(255, 255, 255)' && backgroundColor === 'rgb(255, 255, 255)') {
                        issues++;
                    }
                }
                
                return issues;
            }
        """)
        
        if contrast_issues > 0:
            print(f"⚠️  Potential contrast issues found: {contrast_issues}")
        else:
            print("✅ No obvious contrast issues detected")
    
    async def test_screen_reader_compatibility(self, authenticated_page, server_url):
        """Test screen reader compatibility."""
        print("🔊 Testing Screen Reader Compatibility")
        
        await authenticated_page.goto(f"{server_url}/dashboard/")
        
        # Check for proper heading structure
        headings = await authenticated_page.locator("h1, h2, h3, h4, h5, h6").count()
        assert headings > 0, "No headings found for screen readers"
        
        # Check for alt text on images
        images = await authenticated_page.locator("img").count()
        images_with_alt = await authenticated_page.locator("img[alt]").count()
        
        if images > 0:
            alt_text_ratio = images_with_alt / images
            if alt_text_ratio < 0.8:  # 80% should have alt text
                print(f"⚠️  Only {alt_text_ratio:.1%} of images have alt text")
            else:
                print("✅ Most images have alt text")
        
        # Check for form labels
        inputs = await authenticated_page.locator("input[type='text'], input[type='email'], input[type='password'], select, textarea").count()
        labeled_inputs = await authenticated_page.locator("input[id] + label, label + input, input[aria-label], input[aria-labelledby]").count()
        
        if inputs > 0:
            label_ratio = labeled_inputs / inputs
            if label_ratio < 0.8:  # 80% should be properly labeled
                print(f"⚠️  Only {label_ratio:.1%} of form inputs are properly labeled")
            else:
                print("✅ Most form inputs are properly labeled")
        
        print("✅ Screen reader compatibility checks completed")


@pytest.mark.asyncio
class TestPerformanceAndScalability:
    """Test system performance and scalability."""
    
    async def test_page_load_performance(self, authenticated_page, server_url):
        """Test page load performance across different pages."""
        print("⚡ Testing Page Load Performance")
        
        pages_to_test = [
            ("/dashboard/", "Dashboard"),
            ("/customers/", "Customers"),
            ("/transactions/", "Transactions"),
            ("/reports/", "Reports"),
        ]
        
        performance_results = []
        
        for url, name in pages_to_test:
            start_time = await authenticated_page.evaluate("performance.now()")
            await authenticated_page.goto(f"{server_url}{url}")
            await authenticated_page.wait_for_load_state('networkidle')
            end_time = await authenticated_page.evaluate("performance.now()")
            
            load_time = end_time - start_time
            performance_results.append((name, load_time))
            
            print(f"   {name}: {load_time:.2f}ms")
            
            # Each page should load within reasonable time
            assert load_time < 10000, f"{name} page load too slow: {load_time}ms"
        
        # Calculate average load time
        avg_load_time = sum(time for _, time in performance_results) / len(performance_results)
        print(f"📊 Average page load time: {avg_load_time:.2f}ms")
        
        assert avg_load_time < 5000, f"Average page load time too slow: {avg_load_time}ms"
        
        print("✅ Page load performance acceptable")
    
    async def test_memory_usage(self, authenticated_page, server_url):
        """Test memory usage during navigation."""
        print("🧠 Testing Memory Usage")
        
        # Navigate through several pages
        pages = ["/dashboard/", "/customers/", "/transactions/", "/reports/"]
        
        initial_memory = await authenticated_page.evaluate("""
            () => performance.memory ? performance.memory.usedJSHeapSize : 0
        """)
        
        for page_url in pages * 3:  # Visit each page 3 times
            await authenticated_page.goto(f"{server_url}{page_url}")
            await authenticated_page.wait_for_load_state('networkidle')
            await asyncio.sleep(0.5)
        
        final_memory = await authenticated_page.evaluate("""
            () => performance.memory ? performance.memory.usedJSHeapSize : 0
        """)
        
        if initial_memory > 0 and final_memory > 0:
            memory_increase = final_memory - initial_memory
            memory_increase_mb = memory_increase / (1024 * 1024)
            
            print(f"📊 Memory increase: {memory_increase_mb:.2f}MB")
            
            # Memory increase should be reasonable
            if memory_increase_mb > 50:  # 50MB increase is concerning
                print(f"⚠️  High memory increase detected: {memory_increase_mb:.2f}MB")
            else:
                print("✅ Memory usage is reasonable")
        else:
            print("ℹ️  Memory monitoring not available in this browser")


@pytest.mark.asyncio
class TestDataIntegrity:
    """Test data integrity and consistency."""
    
    async def test_transaction_data_consistency(self, authenticated_page, server_url, test_data_manager):
        """Test transaction data consistency."""
        print("🔍 Testing Transaction Data Consistency")
        
        # Create a test customer
        customer = test_data_manager.create_customer(
            first_name="Test",
            last_name="Consistency",
            phone_number="+1234567890"
        )
        
        # Navigate to transaction form
        await authenticated_page.goto(f"{server_url}/transactions/type/EXCHANGE/")
        
        # Wait for form to load
        await authenticated_page.wait_for_selector("#transaction-form")
        await authenticated_page.wait_for_selector("#customer option:nth-child(2)")
        
        # Fill form with test data
        await authenticated_page.select_option("#customer", index=1)
        await authenticated_page.select_option("#from_currency", label="USD")
        await authenticated_page.select_option("#to_currency", label="AED")
        await authenticated_page.fill("#from_amount", "100")
        
        # Wait for calculations
        await asyncio.sleep(1)
        
        # Get calculated values
        to_amount = await authenticated_page.input_value("#to_amount")
        exchange_rate = await authenticated_page.input_value("#exchange_rate")
        
        # Verify calculation consistency
        if to_amount and exchange_rate:
            calculated = 100 * float(exchange_rate)
            actual = float(to_amount)
            
            # Allow small rounding differences
            assert abs(calculated - actual) < 0.01, f"Calculation inconsistency: {calculated} != {actual}"
            print("✅ Transaction calculations are consistent")
        
        print("✅ Data integrity checks passed")


@pytest.mark.asyncio
class TestSystemStability:
    """Test system stability under various conditions."""

    async def test_rapid_navigation(self, authenticated_page, server_url):
        """Test rapid navigation between pages."""
        print("🏃 Testing Rapid Navigation")

        pages = ["/dashboard/", "/customers/", "/transactions/", "/reports/"]

        # Rapidly navigate between pages
        for _ in range(10):
            for page_url in pages:
                await authenticated_page.goto(f"{server_url}{page_url}")
                # Don't wait for full load, just basic navigation
                await asyncio.sleep(0.1)

        # Final check - should still be functional
        await authenticated_page.goto(f"{server_url}/dashboard/")
        await authenticated_page.wait_for_load_state('networkidle')

        # Verify no errors accumulated
        error_report = await authenticated_page.evaluate("""
            () => window.errorMonitor ? window.errorMonitor.getErrorReport() : {totalErrors: 0}
        """)

        assert error_report.get('totalErrors', 0) < 5, "Too many errors during rapid navigation"
        print("✅ System stable during rapid navigation")

    async def test_concurrent_form_submissions(self, browser, server_url, test_user):
        """Test concurrent form submissions."""
        print("⚡ Testing Concurrent Form Submissions")

        # Create multiple browser contexts
        contexts = []
        pages = []

        try:
            for i in range(3):
                context = await browser.new_context()
                page = await context.new_page()
                contexts.append(context)
                pages.append(page)

                # Login each page
                await page.goto(f"{server_url}/accounts/login/")
                await page.fill("#id_username", test_user.username)
                await page.fill("#id_password", "testpass123")
                await page.click('button[type="submit"]')
                await page.wait_for_url(f"{server_url}/dashboard/")

            # Perform concurrent operations
            tasks = []
            for i, page in enumerate(pages):
                task = self._perform_transaction_operation(page, server_url, i)
                tasks.append(task)

            # Wait for all operations to complete
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check results
            successful_operations = sum(1 for result in results if not isinstance(result, Exception))
            print(f"✅ {successful_operations}/{len(results)} concurrent operations successful")

        finally:
            # Clean up contexts
            for context in contexts:
                await context.close()

    async def _perform_transaction_operation(self, page, server_url, index):
        """Perform a transaction operation for concurrent testing."""
        await page.goto(f"{server_url}/transactions/type/EXCHANGE/")
        await page.wait_for_selector("#transaction-form")

        # Fill form quickly
        await page.select_option("#customer", index=1)
        await page.select_option("#from_currency", label="USD")
        await page.select_option("#to_currency", label="AED")
        await page.fill("#from_amount", str(100 + index))

        # Submit as draft to avoid actual transaction creation
        save_button = page.locator('button[data-action="save"]')
        if await save_button.count() > 0:
            await save_button.click()

        return f"Operation {index} completed"
